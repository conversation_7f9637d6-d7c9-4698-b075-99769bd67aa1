<?php

use App\Http\Controllers\DashboardController;
use App\Http\Controllers\UserAgreementController;
use App\Http\Controllers\WelcomeController;
use App\Http\Controllers\Admin\SubmissionController;
use App\Http\Controllers\Admin\AdminRecommendationController;
use App\Http\Controllers\Admin\SystemSettingController;
use App\Http\Controllers\Admin\RecruitmentPeriodController;
use App\Http\Controllers\Admin\SystemLogController;
use App\Http\Controllers\Admin\EmailLogController;
use App\Http\Controllers\Admin\MailSettingsController;
use App\Http\Controllers\Admin\LoginLogController;
use App\Http\Controllers\Admin\PdfMergeTaskController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| 主要 Web 路由
|--------------------------------------------------------------------------
|
| 這裡定義應用程式的主要 Web 路由，包括首頁、儀表板和使用者協議等核心功能
|
*/

// 首頁 - 系統歡迎頁面
Route::get('/', [WelcomeController::class, 'index'])->name('home');

// 認證後路由 (需要登入但不需要同意使用者協議)
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/user-agreement', [UserAgreementController::class, 'show'])->name('user-agreement.show'); // 使用者協議頁面
    Route::post('/user-agreement', [UserAgreementController::class, 'store'])->name('user-agreement.store'); // 使用者同意協議
});

// 受保護路由 (需要登入且同意使用者協議)
Route::middleware(['auth',  'check.user.agreement', 'check.system.access'])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard'); // 主儀表板 - 根據使用者角色顯示對應的儀表板
});

/**
 * 管理員專用路由
 * 
 * 後台管理頁面與相關功能
 * middleware:
 * - auth: 確保使用者已登入
 * - role:admin: 確保使用者為管理員角色
 * - check.system.access: 確保使用者有權限訪問系統
 */
Route::middleware(['auth',  'role:admin', 'check.system.access'])->prefix('admin')->name('admin.')->group(function () {
    /** 推薦函管理 - 查看所有推薦函 */
    Route::get('/recommendations', [AdminRecommendationController::class, 'index'])->name('recommendations.index');

    /** 系統設定管理 */
    Route::prefix('system-settings')->name('system-settings.')->group(function () {
        Route::get('/', [SystemSettingController::class, 'index'])->name('index'); // 系統設定首頁
        Route::put('/', [SystemSettingController::class, 'update'])->name('update'); // 更新系統設定
        Route::put('/{key}', [SystemSettingController::class, 'updateSingle'])->name('update-single');
        Route::post('/reset-defaults', [SystemSettingController::class, 'resetDefaults'])->name('reset-defaults'); // 重置系統設定為預設值
        Route::post('/toggle-maintenance-mode', [SystemSettingController::class, 'toggleMaintenanceMode'])->name('toggle-maintenance-mode'); // 維護模式開關
        Route::post('/sync-external-data', [SystemSettingController::class, 'syncExternalData'])->name('sync-external-data'); // 同步外部數據
        Route::get('/check-api-connection', [SystemSettingController::class, 'checkApiConnection'])->name('check-api-connection'); // 檢查API連線
        Route::get('/status', [SystemSettingController::class, 'getSystemStatus'])->name('status'); // 取得系統狀態
    });

    /**
     * 提交管理
     *
     * 統一管理推薦函提交方式，包括問卷模板管理與提交方式設定
     * 整合原本的PDF管理和問卷模板功能
     */
    Route::prefix('submission')->name('submission.')->group(function () {
        Route::get('/', [SubmissionController::class, 'index'])->name('index'); // 提交管理主頁面
        Route::post('/submission-settings', [SubmissionController::class, 'updateSubmissionSettings'])->name('submission-settings'); // 更新提交方式設定
        Route::get('/settings', [SubmissionController::class, 'getSettings'])->name('settings'); // 取得設定資訊

        // 問卷模板管理
        Route::post('/toggle/{id}', [SubmissionController::class, 'toggleTemplate'])->name('toggle'); // 啟用/停用問卷模板
        Route::post('/upload-csv', [SubmissionController::class, 'uploadCsvTemplate'])->name('upload-csv'); // 上傳CSV模板
        Route::post('/save-template', [SubmissionController::class, 'saveTemplate'])->name('save-template'); // 儲存問卷模板
        Route::delete('/templates/{id}', [SubmissionController::class, 'destroyTemplate'])->name('templates.destroy'); // 刪除問卷模板
        Route::get('/template/{recommendationId}', [SubmissionController::class, 'getTemplate'])->name('template'); // 取得問卷模板

        // 資料合併作業(開發中，未實作)
        // Route::post('/start-merge', [SubmissionController::class, 'startDataMerge'])->name('start-merge'); // 開始資料合併作業
        // Route::get('/download-merge/{filename}', [SubmissionController::class, 'downloadMergeResult'])->name('download-merge'); // 下載合併結果
    });

    /** 招生期間管理 */
    Route::prefix('recruitment-periods')->name('recruitment-periods.')->group(function () {
        Route::get('/', [RecruitmentPeriodController::class, 'index'])->name('index'); // 招生期間列表
        Route::post('/sync', [RecruitmentPeriodController::class, 'sync'])->name('sync'); // 同步招生期間
        Route::get('/{examId}', [RecruitmentPeriodController::class, 'show'])->name('show'); // 查看招生期間
        Route::get('/status/check', [RecruitmentPeriodController::class, 'checkStatus'])->name('check-status'); // 檢查招生期間狀態
        Route::post('/clear-cache', [RecruitmentPeriodController::class, 'clearCache'])->name('clear-cache'); // 清除招生期間快取
        Route::get('/export/data', [RecruitmentPeriodController::class, 'export'])->name('export'); // 匯出招生期間資料
    });

    /** 系統日誌管理 */
    Route::prefix('system-logs')->name('system-logs.')->group(function () {
        Route::get('/', [SystemLogController::class, 'index'])->name('index'); // 系統日誌列表
        Route::get('/{id}', [SystemLogController::class, 'show'])->name('show'); // 查看系統日誌
        Route::post('/cleanup', [SystemLogController::class, 'cleanup'])->name('cleanup'); // 清理系統日誌
        Route::get('/export', [SystemLogController::class, 'export'])->name('export'); // 匯出系統日誌
    });


    /** Email 日誌管理 */
    Route::prefix('email-logs')->name('email-logs.')->group(function () {
        Route::get('/', [EmailLogController::class, 'index'])->name('index'); // Email 日誌列表
        Route::get('/{id}', [EmailLogController::class, 'show'])->name('show'); // 查看 Email 日誌
        Route::get('/export', [EmailLogController::class, 'export'])->name('export'); // 匯出 Email 日誌
        Route::post('/cleanup', [EmailLogController::class, 'cleanup'])->name('cleanup'); // 清理 Email 日誌
    });

    /** 郵件設定管理 */
    Route::prefix('mail-settings')->name('mail-settings.')->group(function () {
        Route::get('/', [MailSettingsController::class, 'index'])->name('index'); // 郵件設定列表
        Route::put('/', [MailSettingsController::class, 'update'])->name('update'); // 更新郵件設定
        Route::post('/test', [MailSettingsController::class, 'testApi'])->name('test'); // 測試郵件API
        Route::get('/stats', [MailSettingsController::class, 'getStats'])->name('stats'); // 獲取郵件統計
    });

    /** 登入記錄管理 */
    Route::prefix('login-logs')->name('login-logs.')->group(function () {
        Route::get('/', [LoginLogController::class, 'index'])->name('index'); // 登入記錄列表
        Route::get('/{id}', [LoginLogController::class, 'show'])->name('show'); // 查看登入記錄
        Route::post('/cleanup', [LoginLogController::class, 'cleanup'])->name('cleanup'); // 清理登入記錄
        Route::get('/export', [LoginLogController::class, 'export'])->name('export'); // 匯出登入記錄
    });

    /** PDF壓縮任務管理 */
    Route::prefix('tasks')->name('tasks.')->group(function () {
        Route::get('/', [PdfMergeTaskController::class, 'index'])->name('index'); // 任務列表
        Route::post('/', [PdfMergeTaskController::class, 'store'])->name('store'); // 創建任務
        Route::get('/{task}', [PdfMergeTaskController::class, 'show'])->name('show'); // 查看任務詳情
        Route::get('/{task}/download', [PdfMergeTaskController::class, 'download'])->name('download'); // 下載任務結果
        Route::get('/{task}/logs', [PdfMergeTaskController::class, 'logs'])->name('logs'); // 查看任務日誌
        Route::post('/{task}/retry', [PdfMergeTaskController::class, 'retry'])->name('retry'); // 重試任務
        Route::post('/{task}/cancel', [PdfMergeTaskController::class, 'cancel'])->name('cancel'); // 取消任務
        Route::delete('/{task}', [PdfMergeTaskController::class, 'destroy'])->name('destroy'); // 刪除任務
        Route::get('/available-exams', [PdfMergeTaskController::class, 'getAvailableExams'])->name('available-exams'); // 獲取可用考試
        Route::post('/cleanup', [PdfMergeTaskController::class, 'cleanup'])->name('cleanup'); // 清理過期任務
    });
});


/** 認證相關路由 (登入、登出) */
require __DIR__ . '/auth.php';

/** 推薦函功能相關路由 */
require __DIR__ . '/recommendation.php';
