[2025-07-25 14:23:13] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-25 14:23:13] local.INFO: PDF合併任務已啟動 {"task_id":"merge_1adY64VxU5crntMb_1753424593","parameters":{"exam_id":"2","exam_year":114,"requested_at":"2025-07-25T06:23:13.242083Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-25 14:23:14] local.INFO: 開始處理PDF合併任務 {"task_id":"merge_nqzeii3fMzYGEkRt_1753424295","parameters":{"exam_id":"2","exam_year":114,"requested_at":"2025-07-25T06:18:15.225735Z","client_ip":"127.0.0.1"}} 
[2025-07-25 14:23:14] local.INFO: 開始PDF合併處理 {"applicant_id":1,"file_count":2,"available_methods":["fpdi","tcpdf","dompdf"]} 
[2025-07-25 14:23:14] local.INFO: 嘗試使用FPDI合併 {"applicant_id":1,"method":"fpdi","file_count":2,"files":[{"path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1_2025-07-25_11-20-10.pdf","exists":true,"size":47140},{"path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/2_2025-07-25_11-20-34.pdf","exists":true,"size":488789}]} 
[2025-07-25 14:23:14] local.INFO: FPDI合併開始 {"applicant_id":1,"file_count":2} 
[2025-07-25 14:23:14] local.INFO: 開始處理PDF檔案 {"applicant_id":1,"files_to_process":2} 
[2025-07-25 14:23:14] local.INFO: 處理PDF檔案 {"applicant_id":1,"file_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1_2025-07-25_11-20-10.pdf","file_index":0,"file_size":47140} 
[2025-07-25 14:23:14] local.INFO: PDF檔案頁數 {"applicant_id":1,"file_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1_2025-07-25_11-20-10.pdf","page_count":1} 
[2025-07-25 14:23:14] local.DEBUG: 導入PDF頁面 {"applicant_id":1,"file_index":0,"page_no":1,"total_pages":1,"page_size":{"width":209.90277777777774,"height":296.6861111111111,"0":209.90277777777774,"1":296.6861111111111,"orientation":"P"}} 
[2025-07-25 14:23:14] local.INFO: PDF檔案處理完成 {"applicant_id":1,"file_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1_2025-07-25_11-20-10.pdf","pages_added":1,"total_pages_so_far":1} 
[2025-07-25 14:23:14] local.INFO: 處理PDF檔案 {"applicant_id":1,"file_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/2_2025-07-25_11-20-34.pdf","file_index":1,"file_size":488789} 
[2025-07-25 14:23:14] local.INFO: PDF檔案頁數 {"applicant_id":1,"file_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/2_2025-07-25_11-20-34.pdf","page_count":1} 
[2025-07-25 14:23:14] local.DEBUG: 導入PDF頁面 {"applicant_id":1,"file_index":1,"page_no":1,"total_pages":2,"page_size":{"width":211.66666666666666,"height":282.2222222222222,"0":211.66666666666666,"1":282.2222222222222,"orientation":"P"}} 
[2025-07-25 14:23:14] local.INFO: PDF檔案處理完成 {"applicant_id":1,"file_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/2_2025-07-25_11-20-34.pdf","pages_added":1,"total_pages_so_far":2} 
[2025-07-25 14:23:14] local.INFO: 書籤添加完成 {"applicant_id":1,"bookmark_count":2} 
[2025-07-25 14:23:14] local.INFO: FPDI PDF合併成功完成 {"applicant_id":1,"autono":1,"merged_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/temp/merged/1.pdf","processed_files":2,"total_files":2,"total_pages":2,"bookmark_count":2,"final_file_size":539651} 
[2025-07-25 14:23:14] local.INFO: 使用FPDI合併成功完成 {"applicant_id":1,"method":"fpdi","result_file":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/temp/merged/1.pdf","result_size":539651} 
[2025-07-25 14:23:14] local.INFO: 考生PDF合併完成 {"applicant_id":1,"merged_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/temp/merged/1.pdf","file_count":2} 
[2025-07-25 14:23:14] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/temp/merged/1.pdf","zip_name":"348.pdf"} 
[2025-07-25 14:23:14] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-25 14:23:14] local.INFO: ZIP檔案創建成功 {"zip_path":"pdf_merges/merged_recommendations_exam_2_year_114_2025-07-25_14-23-14_merge_nqzeii3fMzYGEkRt_1753424295.zip","added_files":1,"total_files":1,"task_id":"merge_nqzeii3fMzYGEkRt_1753424295"} 
[2025-07-25 14:23:14] local.INFO: PDF合併任務完成 {"task_id":"merge_nqzeii3fMzYGEkRt_1753424295","merged_files":1,"zip_file":"pdf_merges/merged_recommendations_exam_2_year_114_2025-07-25_14-23-14_merge_nqzeii3fMzYGEkRt_1753424295.zip"} 
[2025-07-25 14:23:14] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-25 14:23:42] local.INFO: 管理員取消PDF合併任務 {"task_id":"merge_1adY64VxU5crntMb_1753424593","user_id":1} 
[2025-07-25 14:29:24] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-25T06:29:24.456480Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-25 14:29:24] local.INFO: 招生期間檢查 {"exam_id":"K","check_time":"2025-07-25T06:29:24.456480Z","start_time":"2025-06-05T02:00:00.000000Z","end_time":"2025-06-10T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:29:24] local.INFO: 招生期間檢查 {"exam_id":"E","check_time":"2025-07-25T06:29:24.456480Z","start_time":"2025-04-28T01:00:00.000000Z","end_time":"2025-05-09T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:29:24] local.INFO: 招生期間檢查 {"exam_id":"J","check_time":"2025-07-25T06:29:24.456480Z","start_time":"2025-04-21T01:00:00.000000Z","end_time":"2025-05-02T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:29:24] local.INFO: 招生期間檢查 {"exam_id":"7","check_time":"2025-07-25T06:29:24.456480Z","start_time":"2025-04-08T02:00:00.000000Z","end_time":"2025-05-01T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:29:24] local.INFO: 招生期間檢查 {"exam_id":"1","check_time":"2025-07-25T06:29:24.456480Z","start_time":"2025-03-18T01:00:00.000000Z","end_time":"2025-04-08T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:29:24] local.INFO: 招生期間檢查 {"exam_id":"P","check_time":"2025-07-25T06:29:24.456480Z","start_time":"2025-02-18T01:00:00.000000Z","end_time":"2025-03-25T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:29:24] local.INFO: 招生期間檢查 {"exam_id":"3","check_time":"2025-07-25T06:29:24.456480Z","start_time":"2025-01-03T01:00:00.000000Z","end_time":"2025-01-22T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:29:24] local.INFO: 招生期間檢查 {"exam_id":"B","check_time":"2025-07-25T06:29:24.456480Z","start_time":"2024-12-03T01:00:00.000000Z","end_time":"2025-02-27T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:29:24] local.INFO: 招生期間檢查 {"exam_id":"I","check_time":"2025-07-25T06:29:24.456480Z","start_time":"2024-10-25T01:00:00.000000Z","end_time":"2024-11-11T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:35:39] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/pdf-merge/download","method":"GET"} 
[2025-07-25 14:35:47] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/pdf-merge/download","method":"GET"} 
[2025-07-25 14:35:47] local.INFO: PDF合併檔案下載 {"task_id":"merge_nqzeii3fMzYGEkRt_1753424295","file_path":"pdf_merges/merged_recommendations_exam_2_year_114_2025-07-25_14-23-14_merge_nqzeii3fMzYGEkRt_1753424295.zip","client_ip":"127.0.0.1"} 
[2025-07-25 14:39:32] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/pdf-merge/download","method":"GET"} 
[2025-07-25 14:39:32] local.INFO: PDF合併檔案下載 {"task_id":"merge_nqzeii3fMzYGEkRt_1753424295","file_path":"pdf_merges/merged_recommendations_exam_2_year_114_2025-07-25_14-23-14_merge_nqzeii3fMzYGEkRt_1753424295.zip","client_ip":"127.0.0.1"} 
[2025-07-25 14:43:35] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-25T06:43:35.265163Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-25 14:43:35] local.INFO: 招生期間檢查 {"exam_id":"K","check_time":"2025-07-25T06:43:35.265163Z","start_time":"2025-06-05T02:00:00.000000Z","end_time":"2025-06-10T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:43:35] local.INFO: 招生期間檢查 {"exam_id":"E","check_time":"2025-07-25T06:43:35.265163Z","start_time":"2025-04-28T01:00:00.000000Z","end_time":"2025-05-09T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:43:35] local.INFO: 招生期間檢查 {"exam_id":"J","check_time":"2025-07-25T06:43:35.265163Z","start_time":"2025-04-21T01:00:00.000000Z","end_time":"2025-05-02T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:43:35] local.INFO: 招生期間檢查 {"exam_id":"7","check_time":"2025-07-25T06:43:35.265163Z","start_time":"2025-04-08T02:00:00.000000Z","end_time":"2025-05-01T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:43:35] local.INFO: 招生期間檢查 {"exam_id":"1","check_time":"2025-07-25T06:43:35.265163Z","start_time":"2025-03-18T01:00:00.000000Z","end_time":"2025-04-08T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:43:35] local.INFO: 招生期間檢查 {"exam_id":"P","check_time":"2025-07-25T06:43:35.265163Z","start_time":"2025-02-18T01:00:00.000000Z","end_time":"2025-03-25T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:43:35] local.INFO: 招生期間檢查 {"exam_id":"3","check_time":"2025-07-25T06:43:35.265163Z","start_time":"2025-01-03T01:00:00.000000Z","end_time":"2025-01-22T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:43:35] local.INFO: 招生期間檢查 {"exam_id":"B","check_time":"2025-07-25T06:43:35.265163Z","start_time":"2024-12-03T01:00:00.000000Z","end_time":"2025-02-27T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:43:35] local.INFO: 招生期間檢查 {"exam_id":"I","check_time":"2025-07-25T06:43:35.265163Z","start_time":"2024-10-25T01:00:00.000000Z","end_time":"2024-11-11T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:44:20] local.INFO: 開始同步招生期間資料 {"api_url":"http://localhost:18001/index.php/api/v1/recommendation_system/sync_exam_period"} 
[2025-07-25 14:44:38] local.INFO: 招生期間資料已保存 {"periods_count":10,"data":[{"exam_id":"2","exam_name":"碩士班甄試招生(測試)","app_date1_start":"2025/07/01 09:00:00","app_date1_end":"2025/08/30 17:00:00","synced_at":"2025-07-25T06:44:37.975322Z"},{"exam_id":"K","exam_name":"技優甄審指定項目甄審報名(測試)","app_date1_start":"2025/06/05 10:00:00","app_date1_end":"2025/06/10 17:00:00","synced_at":"2025-07-25T06:44:37.975720Z"},{"exam_id":"E","exam_name":"單獨招收身心障礙學生考試(測試)","app_date1_start":"2025/04/28 09:00:00","app_date1_end":"2025/05/09 17:00:00","synced_at":"2025-07-25T06:44:37.975791Z"},{"exam_id":"J","exam_name":"新住民入學招生(測試)","app_date1_start":"2025/04/21 09:00:00","app_date1_end":"2025/05/02 17:00:00","synced_at":"2025-07-25T06:44:37.975854Z"},{"exam_id":"7","exam_name":"大學申請入學指定項目甄試(測試)","app_date1_start":"2025/04/08 10:00:00","app_date1_end":"2025/05/01 17:00:00","synced_at":"2025-07-25T06:44:37.975914Z"},{"exam_id":"1","exam_name":"博士班考試招生(測試)","app_date1_start":"2025/03/18 09:00:00","app_date1_end":"2025/04/08 17:00:00","synced_at":"2025-07-25T06:44:37.975981Z"},{"exam_id":"P","exam_name":"碩士在職專班招生(測試)","app_date1_start":"2025/02/18 09:00:00","app_date1_end":"2025/03/25 17:00:00","synced_at":"2025-07-25T06:44:37.976080Z"},{"exam_id":"3","exam_name":"碩士班考試招生(測試)","app_date1_start":"2025/01/03 09:00:00","app_date1_end":"2025/01/22 17:00:00","synced_at":"2025-07-25T06:44:37.976159Z"},{"exam_id":"B","exam_name":"原住民專班招生(測試)","app_date1_start":"2024/12/03 09:00:00","app_date1_end":"2025/02/27 17:00:00","synced_at":"2025-07-25T06:44:37.976216Z"},{"exam_id":"I","exam_name":"特殊選才單獨招生(測試)","app_date1_start":"2024/10/25 09:00:00","app_date1_end":"2024/11/11 17:00:00","synced_at":"2025-07-25T06:44:37.976273Z"}]} 
[2025-07-25 14:44:39] local.INFO: 系統初始化時成功同步外部資料 {"success":true,"message":"系統設定同步完成","results":{"exam_periods":{"success":true,"message":"招生期間資料同步成功","data":{"updated_count":10,"processed_periods":[{"exam_id":"2","exam_name":"碩士班甄試招生(測試)","app_date1_start":"2025/07/01 09:00:00","app_date1_end":"2025/08/30 17:00:00","synced_at":"2025-07-25T06:44:37.975322Z"},{"exam_id":"K","exam_name":"技優甄審指定項目甄審報名(測試)","app_date1_start":"2025/06/05 10:00:00","app_date1_end":"2025/06/10 17:00:00","synced_at":"2025-07-25T06:44:37.975720Z"},{"exam_id":"E","exam_name":"單獨招收身心障礙學生考試(測試)","app_date1_start":"2025/04/28 09:00:00","app_date1_end":"2025/05/09 17:00:00","synced_at":"2025-07-25T06:44:37.975791Z"},{"exam_id":"J","exam_name":"新住民入學招生(測試)","app_date1_start":"2025/04/21 09:00:00","app_date1_end":"2025/05/02 17:00:00","synced_at":"2025-07-25T06:44:37.975854Z"},{"exam_id":"7","exam_name":"大學申請入學指定項目甄試(測試)","app_date1_start":"2025/04/08 10:00:00","app_date1_end":"2025/05/01 17:00:00","synced_at":"2025-07-25T06:44:37.975914Z"},{"exam_id":"1","exam_name":"博士班考試招生(測試)","app_date1_start":"2025/03/18 09:00:00","app_date1_end":"2025/04/08 17:00:00","synced_at":"2025-07-25T06:44:37.975981Z"},{"exam_id":"P","exam_name":"碩士在職專班招生(測試)","app_date1_start":"2025/02/18 09:00:00","app_date1_end":"2025/03/25 17:00:00","synced_at":"2025-07-25T06:44:37.976080Z"},{"exam_id":"3","exam_name":"碩士班考試招生(測試)","app_date1_start":"2025/01/03 09:00:00","app_date1_end":"2025/01/22 17:00:00","synced_at":"2025-07-25T06:44:37.976159Z"},{"exam_id":"B","exam_name":"原住民專班招生(測試)","app_date1_start":"2024/12/03 09:00:00","app_date1_end":"2025/02/27 17:00:00","synced_at":"2025-07-25T06:44:37.976216Z"},{"exam_id":"I","exam_name":"特殊選才單獨招生(測試)","app_date1_start":"2024/10/25 09:00:00","app_date1_end":"2024/11/11 17:00:00","synced_at":"2025-07-25T06:44:37.976273Z"}]}}}} 
[2025-07-25 14:45:08] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/auth-from-external","method":"GET"} 
[2025-07-25 14:45:12] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/auth-from-external","method":"GET"} 
[2025-07-25 14:45:14] local.INFO: 建立新使用者帳號 {"user_id":2,"email":"<EMAIL>","role":"applicant"} 
[2025-07-25 14:45:14] local.INFO: 建立新考生記錄 {"user_id":2,"applicant_id":1,"external_uid":"ZmOnRv1mNU6+zsms5A1fCg==","exam_year":"114","exam_id":"2"} 
[2025-07-25 14:45:14] local.INFO: 考生eapapi登入成功 {"external_uid":"ZmOnRv1mNU6+zsms5A1fCg==","exam_year":114,"exam_id":"2","user_id":2,"applicant_id":1,"ip":"127.0.0.1"} 
[2025-07-25 14:45:18] local.INFO: 考生eapapi登入成功 {"external_uid":"ZmOnRv1mNU6+zsms5A1fCg==","exam_year":114,"exam_id":"2","user_id":2,"applicant_id":1,"ip":"127.0.0.1"} 
[2025-07-25 14:45:29] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-25T06:45:29.877230Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-25 14:45:29] local.INFO: 【Middleware】考生存取檢查中間件觸發 {"user":2} 
[2025-07-25 14:45:29] local.INFO: 考生嘗試建立推薦函: {"user_id":2,"applicant_id":1,"request_data":{"recommender_email":"<EMAIL>","recommender_name":"吳宗霖","recommender_title":null,"recommender_phone":null,"recommender_department":null,"department_name":"機械工程學系碩士班","program_type":"碩士班甄試招生(測試)","external_autono":348}} 
[2025-07-25 14:45:29] local.INFO: 建立新的推薦人使用者帳號 {"user_id":3,"email":"<EMAIL>","name":"吳宗霖"} 
[2025-07-25 14:45:29] local.INFO: 嘗試新增推薦人資料 {"user_id":3,"email":"<EMAIL>","name":"吳宗霖","title":"","department":"","phone":"","login_token":"尚未生成","exam_year":114,"exam_id":"2"} 
[2025-07-25 14:45:29] local.DEBUG: Generated secure login token {"token_length":64,"timestamp":1753425929} 
[2025-07-25 14:45:29] local.INFO: 建立新的推薦人資料 {"recommender_id":1,"user_id":3,"email":"<EMAIL>","exam_year":114,"exam_id":"2"} 
[2025-07-25 14:45:30] local.INFO: 外部 API 郵件發送成功 {"recipient":"<EMAIL>","subject":"推薦函邀請 – 碩士班甄試招生(測試)（機械工程學系碩士班）","api_response":"{\"status\":\"success\",\"detail\":\"Message has been sent\"}"} 
[2025-07-25 14:45:30] local.INFO: 推薦函邀請信發送成功 {"recommendation_id":1,"recommender_email":"<EMAIL>","email_log_id":1} 
[2025-07-25 14:45:38] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-25T06:45:38.446979Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-25 14:45:38] local.INFO: 【Middleware】考生存取檢查中間件觸發 {"user":2} 
[2025-07-25 14:45:38] local.INFO: 考生嘗試建立推薦函: {"user_id":2,"applicant_id":1,"request_data":{"recommender_email":"<EMAIL>","recommender_name":"Mike","recommender_title":null,"recommender_phone":null,"recommender_department":null,"department_name":"資訊工程學系碩士班","program_type":"碩士班甄試招生(測試)","external_autono":349}} 
[2025-07-25 14:45:38] local.INFO: 建立新的推薦人使用者帳號 {"user_id":4,"email":"<EMAIL>","name":"Mike"} 
[2025-07-25 14:45:38] local.INFO: 嘗試新增推薦人資料 {"user_id":4,"email":"<EMAIL>","name":"Mike","title":"","department":"","phone":"","login_token":"尚未生成","exam_year":114,"exam_id":"2"} 
[2025-07-25 14:45:38] local.DEBUG: Generated secure login token {"token_length":64,"timestamp":1753425938} 
[2025-07-25 14:45:38] local.INFO: 建立新的推薦人資料 {"recommender_id":2,"user_id":4,"email":"<EMAIL>","exam_year":114,"exam_id":"2"} 
[2025-07-25 14:45:38] local.INFO: 外部 API 郵件發送成功 {"recipient":"<EMAIL>","subject":"推薦函邀請 – 碩士班甄試招生(測試)（資訊工程學系碩士班）","api_response":"{\"status\":\"success\",\"detail\":\"Message has been sent\"}"} 
[2025-07-25 14:45:38] local.INFO: 推薦函邀請信發送成功 {"recommendation_id":2,"recommender_email":"<EMAIL>","email_log_id":2} 
[2025-07-25 14:45:52] local.INFO: 推薦人登入成功 {"recommender_id":1,"user_id":3,"token":"9331b3c7dfced25ffefce5f39525eae80655abd3127ce37df62236638b45a5c7","ip":"127.0.0.1"} 
[2025-07-25 14:46:10] local.INFO: 使用者登出 {"user_type":"admin","ip":"127.0.0.1"} 
[2025-07-25 14:46:42] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-25T06:46:42.807969Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-25 14:46:42] local.INFO: 【Middleware】推薦人存取檢查中間件觸發 {"user":3} 
[2025-07-25 14:46:42] local.INFO: PDF檔案已存儲 {"recommendation_id":1,"file_path":"recommendations/2/114/348/1_2025-07-25_14-46-42.pdf","file_size":973,"original_name":"1.pdf"} 
[2025-07-25 14:46:42] local.INFO: 推薦函 PDF 上傳成功 {"recommendation_id":"1","file_path":"recommendations/2/114/348/1_2025-07-25_14-46-42.pdf","file_size":973} 
[2025-07-25 14:46:43] local.INFO: 外部 API 郵件發送成功 {"recipient":"<EMAIL>","subject":"推薦函已提交通知 – 碩士班甄試招生(測試)（機械工程學系碩士班）","api_response":"{\"status\":\"success\",\"detail\":\"Message has been sent\"}"} 
[2025-07-25 14:46:43] local.INFO: 推薦函提交通知信發送成功 {"recommendation_id":1,"applicant_email":"<EMAIL>","email_log_id":3} 
[2025-07-25 14:46:43] local.INFO: 推薦函提交成功 {"recommendation_id":"1","submission_type":"pdf","user_id":3} 
[2025-07-25 14:46:45] local.INFO: 使用者登出 {"user_type":"admin","ip":"127.0.0.1"} 
[2025-07-25 14:46:52] local.INFO: 推薦人登入成功 {"recommender_id":2,"user_id":4,"token":"5736cb622e22682f1cfe00dcd7ecad38b449eb95aa3d2da8f77a1728de828f39","ip":"127.0.0.1"} 
[2025-07-25 14:47:07] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-25T06:47:07.990498Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-25 14:47:07] local.INFO: 【Middleware】推薦人存取檢查中間件觸發 {"user":4} 
[2025-07-25 14:47:08] local.INFO: PDF檔案已存儲 {"recommendation_id":2,"file_path":"recommendations/2/114/349/2_2025-07-25_14-47-08.pdf","file_size":488789,"original_name":"Group 1.pdf"} 
[2025-07-25 14:47:08] local.INFO: 推薦函 PDF 上傳成功 {"recommendation_id":"2","file_path":"recommendations/2/114/349/2_2025-07-25_14-47-08.pdf","file_size":488789} 
[2025-07-25 14:47:08] local.INFO: 外部 API 郵件發送成功 {"recipient":"<EMAIL>","subject":"推薦函已提交通知 – 碩士班甄試招生(測試)（資訊工程學系碩士班）","api_response":"{\"status\":\"success\",\"detail\":\"Message has been sent\"}"} 
[2025-07-25 14:47:08] local.INFO: 推薦函提交通知信發送成功 {"recommendation_id":2,"applicant_email":"<EMAIL>","email_log_id":4} 
[2025-07-25 14:47:08] local.INFO: 推薦函提交成功 {"recommendation_id":"2","submission_type":"pdf","user_id":4} 
[2025-07-25 14:48:38] local.INFO: 使用者登出 {"user_type":"admin","ip":"127.0.0.1"} 
[2025-07-25 14:48:52] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/auth-from-external","method":"GET"} 
[2025-07-25 14:48:57] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/auth-from-external","method":"GET"} 
[2025-07-25 14:48:58] local.INFO: 考生eapapi登入成功 {"external_uid":"ZmOnRv1mNU6+zsms5A1fCg==","exam_year":114,"exam_id":"2","user_id":2,"applicant_id":1,"ip":"127.0.0.1"} 
[2025-07-25 14:49:03] local.INFO: 考生eapapi登入成功 {"external_uid":"ZmOnRv1mNU6+zsms5A1fCg==","exam_year":114,"exam_id":"2","user_id":2,"applicant_id":1,"ip":"127.0.0.1"} 
[2025-07-25 14:49:12] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-25T06:49:12.074662Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-25 14:49:12] local.INFO: 【Middleware】考生存取檢查中間件觸發 {"user":2} 
[2025-07-25 14:49:12] local.INFO: 考生嘗試建立推薦函: {"user_id":2,"applicant_id":1,"request_data":{"recommender_email":"<EMAIL>","recommender_name":"Mike","recommender_title":null,"recommender_phone":null,"recommender_department":null,"department_name":"機械工程學系碩士班","program_type":"碩士班甄試招生(測試)","external_autono":348}} 
[2025-07-25 14:49:12] local.INFO: 更新推薦人考試資訊 {"recommender_id":2,"updated_fields":["exam_year","exam_id"]} 
[2025-07-25 14:49:12] local.INFO: 外部 API 郵件發送成功 {"recipient":"<EMAIL>","subject":"推薦函邀請 – 碩士班甄試招生(測試)（機械工程學系碩士班）","api_response":"{\"status\":\"success\",\"detail\":\"Message has been sent\"}"} 
[2025-07-25 14:49:12] local.INFO: 推薦函邀請信發送成功 {"recommendation_id":3,"recommender_email":"<EMAIL>","email_log_id":5} 
[2025-07-25 14:49:26] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-25T06:49:26.674177Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-25 14:49:26] local.INFO: 【Middleware】推薦人存取檢查中間件觸發 {"user":4} 
[2025-07-25 14:49:26] local.INFO: PDF檔案已存儲 {"recommendation_id":3,"file_path":"recommendations/2/114/348/2_2025-07-25_14-49-26.pdf","file_size":488789,"original_name":"Group 1.pdf"} 
[2025-07-25 14:49:26] local.INFO: 推薦函 PDF 上傳成功 {"recommendation_id":"3","file_path":"recommendations/2/114/348/2_2025-07-25_14-49-26.pdf","file_size":488789} 
[2025-07-25 14:49:27] local.INFO: 外部 API 郵件發送成功 {"recipient":"<EMAIL>","subject":"推薦函已提交通知 – 碩士班甄試招生(測試)（機械工程學系碩士班）","api_response":"{\"status\":\"success\",\"detail\":\"Message has been sent\"}"} 
[2025-07-25 14:49:27] local.INFO: 推薦函提交通知信發送成功 {"recommendation_id":3,"applicant_email":"<EMAIL>","email_log_id":6} 
[2025-07-25 14:49:27] local.INFO: 推薦函提交成功 {"recommendation_id":"3","submission_type":"pdf","user_id":4} 
[2025-07-25 14:49:44] local.INFO: 使用者登出 {"user_type":"admin","ip":"127.0.0.1"} 
[2025-07-25 14:50:20] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-25T06:50:20.441605Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-25 14:50:20] local.INFO: 招生期間檢查 {"exam_id":"K","check_time":"2025-07-25T06:50:20.441605Z","start_time":"2025-06-05T02:00:00.000000Z","end_time":"2025-06-10T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:50:20] local.INFO: 招生期間檢查 {"exam_id":"E","check_time":"2025-07-25T06:50:20.441605Z","start_time":"2025-04-28T01:00:00.000000Z","end_time":"2025-05-09T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:50:20] local.INFO: 招生期間檢查 {"exam_id":"J","check_time":"2025-07-25T06:50:20.441605Z","start_time":"2025-04-21T01:00:00.000000Z","end_time":"2025-05-02T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:50:20] local.INFO: 招生期間檢查 {"exam_id":"7","check_time":"2025-07-25T06:50:20.441605Z","start_time":"2025-04-08T02:00:00.000000Z","end_time":"2025-05-01T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:50:20] local.INFO: 招生期間檢查 {"exam_id":"1","check_time":"2025-07-25T06:50:20.441605Z","start_time":"2025-03-18T01:00:00.000000Z","end_time":"2025-04-08T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:50:20] local.INFO: 招生期間檢查 {"exam_id":"P","check_time":"2025-07-25T06:50:20.441605Z","start_time":"2025-02-18T01:00:00.000000Z","end_time":"2025-03-25T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:50:20] local.INFO: 招生期間檢查 {"exam_id":"3","check_time":"2025-07-25T06:50:20.441605Z","start_time":"2025-01-03T01:00:00.000000Z","end_time":"2025-01-22T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:50:20] local.INFO: 招生期間檢查 {"exam_id":"B","check_time":"2025-07-25T06:50:20.441605Z","start_time":"2024-12-03T01:00:00.000000Z","end_time":"2025-02-27T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:50:20] local.INFO: 招生期間檢查 {"exam_id":"I","check_time":"2025-07-25T06:50:20.441605Z","start_time":"2024-10-25T01:00:00.000000Z","end_time":"2024-11-11T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:50:29] local.INFO: 管理員創建PDF合併任務 {"task_id":"merge_Keg8QfkvaFy3cJ4o_1753426229","parameters":{"exam_id":"2","exam_year":114,"requested_at":"2025-07-25T06:50:29.627982Z","client_ip":"127.0.0.1","created_by":"admin","user_id":1},"user_id":1} 
[2025-07-25 14:52:12] local.INFO: 管理員刪除PDF合併任務 {"task_id":"merge_Keg8QfkvaFy3cJ4o_1753426229","user_id":1} 
[2025-07-25 14:52:30] local.INFO: 管理員創建PDF合併任務 {"task_id":"merge_BoJxEE6HzPgYoBSL_1753426350","parameters":{"exam_id":"2","exam_year":114,"requested_at":"2025-07-25T06:52:30.402350Z","client_ip":"127.0.0.1","created_by":"admin","user_id":1},"user_id":1} 
[2025-07-25 14:52:35] local.INFO: 管理員取消PDF合併任務 {"task_id":"merge_BoJxEE6HzPgYoBSL_1753426350","user_id":1} 
[2025-07-25 14:52:42] local.INFO: 管理員刪除PDF合併任務 {"task_id":"merge_BoJxEE6HzPgYoBSL_1753426350","user_id":1} 
[2025-07-25 14:53:36] local.INFO: 管理員創建PDF合併任務 {"task_id":"merge_WkE6860j2eH2zZjY_1753426416","parameters":{"exam_id":"2","exam_year":114,"requested_at":"2025-07-25T06:53:36.894456Z","client_ip":"127.0.0.1","created_by":"admin","user_id":1},"user_id":1} 
[2025-07-25 14:53:45] local.INFO: 管理員刪除PDF合併任務 {"task_id":"merge_WkE6860j2eH2zZjY_1753426416","user_id":1} 
[2025-07-25 14:54:38] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-25T06:54:38.992943Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-25 14:54:39] local.INFO: 招生期間檢查 {"exam_id":"K","check_time":"2025-07-25T06:54:38.992943Z","start_time":"2025-06-05T02:00:00.000000Z","end_time":"2025-06-10T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:54:39] local.INFO: 招生期間檢查 {"exam_id":"E","check_time":"2025-07-25T06:54:38.992943Z","start_time":"2025-04-28T01:00:00.000000Z","end_time":"2025-05-09T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:54:39] local.INFO: 招生期間檢查 {"exam_id":"J","check_time":"2025-07-25T06:54:38.992943Z","start_time":"2025-04-21T01:00:00.000000Z","end_time":"2025-05-02T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:54:39] local.INFO: 招生期間檢查 {"exam_id":"7","check_time":"2025-07-25T06:54:38.992943Z","start_time":"2025-04-08T02:00:00.000000Z","end_time":"2025-05-01T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:54:39] local.INFO: 招生期間檢查 {"exam_id":"1","check_time":"2025-07-25T06:54:38.992943Z","start_time":"2025-03-18T01:00:00.000000Z","end_time":"2025-04-08T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:54:39] local.INFO: 招生期間檢查 {"exam_id":"P","check_time":"2025-07-25T06:54:38.992943Z","start_time":"2025-02-18T01:00:00.000000Z","end_time":"2025-03-25T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:54:39] local.INFO: 招生期間檢查 {"exam_id":"3","check_time":"2025-07-25T06:54:38.992943Z","start_time":"2025-01-03T01:00:00.000000Z","end_time":"2025-01-22T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:54:39] local.INFO: 招生期間檢查 {"exam_id":"B","check_time":"2025-07-25T06:54:38.992943Z","start_time":"2024-12-03T01:00:00.000000Z","end_time":"2025-02-27T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:54:39] local.INFO: 招生期間檢查 {"exam_id":"I","check_time":"2025-07-25T06:54:38.992943Z","start_time":"2024-10-25T01:00:00.000000Z","end_time":"2024-11-11T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:07:35] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-25T07:07:35.500826Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-25 15:07:35] local.INFO: 招生期間檢查 {"exam_id":"K","check_time":"2025-07-25T07:07:35.500826Z","start_time":"2025-06-05T02:00:00.000000Z","end_time":"2025-06-10T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:07:35] local.INFO: 招生期間檢查 {"exam_id":"E","check_time":"2025-07-25T07:07:35.500826Z","start_time":"2025-04-28T01:00:00.000000Z","end_time":"2025-05-09T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:07:35] local.INFO: 招生期間檢查 {"exam_id":"J","check_time":"2025-07-25T07:07:35.500826Z","start_time":"2025-04-21T01:00:00.000000Z","end_time":"2025-05-02T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:07:35] local.INFO: 招生期間檢查 {"exam_id":"7","check_time":"2025-07-25T07:07:35.500826Z","start_time":"2025-04-08T02:00:00.000000Z","end_time":"2025-05-01T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:07:35] local.INFO: 招生期間檢查 {"exam_id":"1","check_time":"2025-07-25T07:07:35.500826Z","start_time":"2025-03-18T01:00:00.000000Z","end_time":"2025-04-08T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:07:35] local.INFO: 招生期間檢查 {"exam_id":"P","check_time":"2025-07-25T07:07:35.500826Z","start_time":"2025-02-18T01:00:00.000000Z","end_time":"2025-03-25T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:07:35] local.INFO: 招生期間檢查 {"exam_id":"3","check_time":"2025-07-25T07:07:35.500826Z","start_time":"2025-01-03T01:00:00.000000Z","end_time":"2025-01-22T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:07:35] local.INFO: 招生期間檢查 {"exam_id":"B","check_time":"2025-07-25T07:07:35.500826Z","start_time":"2024-12-03T01:00:00.000000Z","end_time":"2025-02-27T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:07:35] local.INFO: 招生期間檢查 {"exam_id":"I","check_time":"2025-07-25T07:07:35.500826Z","start_time":"2024-10-25T01:00:00.000000Z","end_time":"2024-11-11T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:16:24] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-25T07:16:24.761547Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-25 15:16:24] local.INFO: 招生期間檢查 {"exam_id":"K","check_time":"2025-07-25T07:16:24.761547Z","start_time":"2025-06-05T02:00:00.000000Z","end_time":"2025-06-10T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:16:24] local.INFO: 招生期間檢查 {"exam_id":"E","check_time":"2025-07-25T07:16:24.761547Z","start_time":"2025-04-28T01:00:00.000000Z","end_time":"2025-05-09T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:16:24] local.INFO: 招生期間檢查 {"exam_id":"J","check_time":"2025-07-25T07:16:24.761547Z","start_time":"2025-04-21T01:00:00.000000Z","end_time":"2025-05-02T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:16:24] local.INFO: 招生期間檢查 {"exam_id":"7","check_time":"2025-07-25T07:16:24.761547Z","start_time":"2025-04-08T02:00:00.000000Z","end_time":"2025-05-01T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:16:24] local.INFO: 招生期間檢查 {"exam_id":"1","check_time":"2025-07-25T07:16:24.761547Z","start_time":"2025-03-18T01:00:00.000000Z","end_time":"2025-04-08T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:16:24] local.INFO: 招生期間檢查 {"exam_id":"P","check_time":"2025-07-25T07:16:24.761547Z","start_time":"2025-02-18T01:00:00.000000Z","end_time":"2025-03-25T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:16:24] local.INFO: 招生期間檢查 {"exam_id":"3","check_time":"2025-07-25T07:16:24.761547Z","start_time":"2025-01-03T01:00:00.000000Z","end_time":"2025-01-22T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:16:24] local.INFO: 招生期間檢查 {"exam_id":"B","check_time":"2025-07-25T07:16:24.761547Z","start_time":"2024-12-03T01:00:00.000000Z","end_time":"2025-02-27T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:16:24] local.INFO: 招生期間檢查 {"exam_id":"I","check_time":"2025-07-25T07:16:24.761547Z","start_time":"2024-10-25T01:00:00.000000Z","end_time":"2024-11-11T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:24:38] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-25T07:24:38.331377Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-25 15:24:38] local.INFO: 招生期間檢查 {"exam_id":"K","check_time":"2025-07-25T07:24:38.331377Z","start_time":"2025-06-05T02:00:00.000000Z","end_time":"2025-06-10T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:24:38] local.INFO: 招生期間檢查 {"exam_id":"E","check_time":"2025-07-25T07:24:38.331377Z","start_time":"2025-04-28T01:00:00.000000Z","end_time":"2025-05-09T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:24:38] local.INFO: 招生期間檢查 {"exam_id":"J","check_time":"2025-07-25T07:24:38.331377Z","start_time":"2025-04-21T01:00:00.000000Z","end_time":"2025-05-02T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:24:38] local.INFO: 招生期間檢查 {"exam_id":"7","check_time":"2025-07-25T07:24:38.331377Z","start_time":"2025-04-08T02:00:00.000000Z","end_time":"2025-05-01T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:24:38] local.INFO: 招生期間檢查 {"exam_id":"1","check_time":"2025-07-25T07:24:38.331377Z","start_time":"2025-03-18T01:00:00.000000Z","end_time":"2025-04-08T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:24:38] local.INFO: 招生期間檢查 {"exam_id":"P","check_time":"2025-07-25T07:24:38.331377Z","start_time":"2025-02-18T01:00:00.000000Z","end_time":"2025-03-25T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:24:38] local.INFO: 招生期間檢查 {"exam_id":"3","check_time":"2025-07-25T07:24:38.331377Z","start_time":"2025-01-03T01:00:00.000000Z","end_time":"2025-01-22T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:24:38] local.INFO: 招生期間檢查 {"exam_id":"B","check_time":"2025-07-25T07:24:38.331377Z","start_time":"2024-12-03T01:00:00.000000Z","end_time":"2025-02-27T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:24:38] local.INFO: 招生期間檢查 {"exam_id":"I","check_time":"2025-07-25T07:24:38.331377Z","start_time":"2024-10-25T01:00:00.000000Z","end_time":"2024-11-11T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:31:04] local.ERROR: App\Http\Controllers\Admin\EmailLogController::show(): Argument #1 ($id) must be of type int, string given, called in C:\Users\<USER>\Desktop\rec-letter\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php on line 46 {"userId":1,"exception":"[object] (TypeError(code: 0): App\\Http\\Controllers\\Admin\\EmailLogController::show(): Argument #1 ($id) must be of type int, string given, called in C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php on line 46 at C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Controllers\\Admin\\EmailLogController.php:49)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Admin\\EmailLogController->show('export')
#1 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\EmailLogController), 'show')
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\CheckSystemAccess.php(55): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\CheckSystemAccess->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\CheckUserRole.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\CheckUserRole->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'admin')
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\HandleAppearance.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#34 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#57 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Desktop\\rec-letter\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#60 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('C:\\\\Users\\\\<USER>\\\\D...')
#61 {main}
"} 
[2025-07-25 15:32:00] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-25T07:32:00.220768Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-25 15:32:00] local.INFO: 招生期間檢查 {"exam_id":"K","check_time":"2025-07-25T07:32:00.220768Z","start_time":"2025-06-05T02:00:00.000000Z","end_time":"2025-06-10T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:32:00] local.INFO: 招生期間檢查 {"exam_id":"E","check_time":"2025-07-25T07:32:00.220768Z","start_time":"2025-04-28T01:00:00.000000Z","end_time":"2025-05-09T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:32:00] local.INFO: 招生期間檢查 {"exam_id":"J","check_time":"2025-07-25T07:32:00.220768Z","start_time":"2025-04-21T01:00:00.000000Z","end_time":"2025-05-02T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:32:00] local.INFO: 招生期間檢查 {"exam_id":"7","check_time":"2025-07-25T07:32:00.220768Z","start_time":"2025-04-08T02:00:00.000000Z","end_time":"2025-05-01T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:32:00] local.INFO: 招生期間檢查 {"exam_id":"1","check_time":"2025-07-25T07:32:00.220768Z","start_time":"2025-03-18T01:00:00.000000Z","end_time":"2025-04-08T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:32:00] local.INFO: 招生期間檢查 {"exam_id":"P","check_time":"2025-07-25T07:32:00.220768Z","start_time":"2025-02-18T01:00:00.000000Z","end_time":"2025-03-25T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:32:00] local.INFO: 招生期間檢查 {"exam_id":"3","check_time":"2025-07-25T07:32:00.220768Z","start_time":"2025-01-03T01:00:00.000000Z","end_time":"2025-01-22T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:32:00] local.INFO: 招生期間檢查 {"exam_id":"B","check_time":"2025-07-25T07:32:00.220768Z","start_time":"2024-12-03T01:00:00.000000Z","end_time":"2025-02-27T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:32:00] local.INFO: 招生期間檢查 {"exam_id":"I","check_time":"2025-07-25T07:32:00.220768Z","start_time":"2024-10-25T01:00:00.000000Z","end_time":"2024-11-11T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:32:32] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-25T07:32:32.579178Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-25 15:32:32] local.INFO: 招生期間檢查 {"exam_id":"K","check_time":"2025-07-25T07:32:32.579178Z","start_time":"2025-06-05T02:00:00.000000Z","end_time":"2025-06-10T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:32:32] local.INFO: 招生期間檢查 {"exam_id":"E","check_time":"2025-07-25T07:32:32.579178Z","start_time":"2025-04-28T01:00:00.000000Z","end_time":"2025-05-09T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:32:32] local.INFO: 招生期間檢查 {"exam_id":"J","check_time":"2025-07-25T07:32:32.579178Z","start_time":"2025-04-21T01:00:00.000000Z","end_time":"2025-05-02T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:32:32] local.INFO: 招生期間檢查 {"exam_id":"7","check_time":"2025-07-25T07:32:32.579178Z","start_time":"2025-04-08T02:00:00.000000Z","end_time":"2025-05-01T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:32:32] local.INFO: 招生期間檢查 {"exam_id":"1","check_time":"2025-07-25T07:32:32.579178Z","start_time":"2025-03-18T01:00:00.000000Z","end_time":"2025-04-08T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:32:32] local.INFO: 招生期間檢查 {"exam_id":"P","check_time":"2025-07-25T07:32:32.579178Z","start_time":"2025-02-18T01:00:00.000000Z","end_time":"2025-03-25T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:32:32] local.INFO: 招生期間檢查 {"exam_id":"3","check_time":"2025-07-25T07:32:32.579178Z","start_time":"2025-01-03T01:00:00.000000Z","end_time":"2025-01-22T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:32:32] local.INFO: 招生期間檢查 {"exam_id":"B","check_time":"2025-07-25T07:32:32.579178Z","start_time":"2024-12-03T01:00:00.000000Z","end_time":"2025-02-27T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:32:32] local.INFO: 招生期間檢查 {"exam_id":"I","check_time":"2025-07-25T07:32:32.579178Z","start_time":"2024-10-25T01:00:00.000000Z","end_time":"2024-11-11T09:00:00.000000Z","is_in_period":false} 
[2025-07-28 10:17:06] local.INFO: 管理員創建PDF合併任務 {"task_id":"merge_J4yBL4ePrGaxXujk_1753669025","parameters":{"exam_id":"2","exam_year":114,"requested_at":"2025-07-28T02:17:05.824640Z","client_ip":"127.0.0.1","created_by":"admin","user_id":1},"user_id":1} 
[2025-07-28 10:17:30] local.INFO: 管理員刪除PDF合併任務 {"task_id":"merge_J4yBL4ePrGaxXujk_1753669025","user_id":1} 
[2025-07-28 10:17:56] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-28 10:18:39] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-28 13:02:33] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/auth-from-external","method":"GET"} 
[2025-07-28 13:02:40] local.INFO: 考生eapapi登入成功 {"external_uid":"ZmOnRv1mNU6+zsms5A1fCg==","exam_year":114,"exam_id":"2","user_id":2,"applicant_id":1,"ip":"127.0.0.1"} 
[2025-07-28 13:02:46] local.INFO: 使用者登出 {"user_type":"admin","ip":"127.0.0.1"} 
[2025-07-28 13:11:18] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/pdf-merge/download-recommendation/2/114/348/1","method":"GET"} 
[2025-07-28 13:11:18] local.INFO: 下載推薦函請求 {"exam_id":"2","exam_year":"114","autono":"348","index":"1"} 
[2025-07-28 13:11:18] local.ERROR: The file "C:\Users\<USER>\Desktop\rec-letter\storage\app/recommendations/2/114/348/1.pdf" does not exist {"exception":"[object] (Symfony\\Component\\HttpFoundation\\File\\Exception\\FileNotFoundException(code: 0): The file \"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/recommendations/2/114/348/1.pdf\" does not exist at C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\http-foundation\\File\\File.php:36)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\http-foundation\\BinaryFileResponse.php(73): Symfony\\Component\\HttpFoundation\\File\\File->__construct('C:\\\\Users\\\\<USER>\\\\D...')
#1 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\http-foundation\\BinaryFileResponse.php(50): Symfony\\Component\\HttpFoundation\\BinaryFileResponse->setFile('C:\\\\Users\\\\<USER>\\\\D...', NULL, false, true)
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(297): Symfony\\Component\\HttpFoundation\\BinaryFileResponse->__construct('C:\\\\Users\\\\<USER>\\\\D...', 200, Array)
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Controllers\\Api\\PdfMergeApiController.php(390): Illuminate\\Routing\\ResponseFactory->file('C:\\\\Users\\\\<USER>\\\\D...', Array)
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\PdfMergeApiController->download('2', '114', '348', '1')
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\PdfMergeApiController), 'download')
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\ApiWhitelistMiddleware.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ApiWhitelistMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#17 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\rec-letter\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#43 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('C:\\\\Users\\\\<USER>\\\\D...')
#44 {main}
"} 
[2025-07-28 13:13:36] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/pdf-merge/download-recommendation/2/114/348/1","method":"GET"} 
[2025-07-28 13:13:36] local.INFO: 下載推薦函請求 {"exam_id":"2","exam_year":"114","autono":"348","index":"1"} 
[2025-07-28 13:13:36] local.ERROR: Undefined variable $year {"exception":"[object] (ErrorException(code: 0): Undefined variable $year at C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Controllers\\Api\\PdfMergeApiController.php:382)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined varia...', 'C:\\\\Users\\\\<USER>\\\\D...', 382)
#1 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Controllers\\Api\\PdfMergeApiController.php(382): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined varia...', 'C:\\\\Users\\\\<USER>\\\\D...', 382)
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\PdfMergeApiController->download('2', '114', '348', '1')
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\PdfMergeApiController), 'download')
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\ApiWhitelistMiddleware.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ApiWhitelistMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\rec-letter\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#41 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('C:\\\\Users\\\\<USER>\\\\D...')
#42 {main}
"} 
[2025-07-28 13:13:42] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/pdf-merge/download-recommendation/2/114/348/1","method":"GET"} 
[2025-07-28 13:13:42] local.INFO: 下載推薦函請求 {"exam_id":"2","exam_year":"114","autono":"348","index":"1"} 
[2025-07-28 13:13:48] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/pdf-merge/download-recommendation/2/114/348/1","method":"GET"} 
[2025-07-28 13:13:49] local.INFO: 下載推薦函請求 {"exam_id":"2","exam_year":"114","autono":"348","index":"1"} 
[2025-07-28 13:16:47] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/pdf-merge/download-recommendation/2/114/348/1","method":"GET"} 
[2025-07-28 13:16:48] local.INFO: 下載推薦函請求 {"exam_id":"2","exam_year":"114","autono":"348","index":"1"} 
[2025-07-28 13:16:48] local.WARNING: 推薦函檔案不存在：private/recommendations/2/114/348/1.pdf  
[2025-07-28 13:17:38] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/pdf-merge/download-recommendation/2/114/348/1","method":"GET"} 
[2025-07-28 13:17:38] local.INFO: 下載推薦函請求 {"exam_id":"2","exam_year":"114","autono":"348","index":"1"} 
[2025-07-28 13:17:59] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/pdf-merge/download-recommendation/2/114/348/2","method":"GET"} 
[2025-07-28 13:17:59] local.INFO: 下載推薦函請求 {"exam_id":"2","exam_year":"114","autono":"348","index":"2"} 
[2025-07-28 14:16:02] local.INFO: 開始處理PDF壓縮任務 {"task_id":"merge_PrdRkuumQRrNXcAQ_1753683361","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T06:16:01.753240Z","client_ip":"127.0.0.1","created_by":"test_command","test_mode":true}} 
[2025-07-28 14:16:03] local.WARNING: PDF檔案不存在 {"recommendation_id":1,"pdf_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/recommendations/2/114/348/1_2025-07-25_14-46-42.pdf"} 
[2025-07-28 14:16:03] local.WARNING: PDF檔案不存在 {"recommendation_id":2,"pdf_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/recommendations/2/114/349/2_2025-07-25_14-47-08.pdf"} 
[2025-07-28 14:16:03] local.WARNING: PDF檔案不存在 {"recommendation_id":3,"pdf_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/recommendations/2/114/348/2_2025-07-25_14-49-26.pdf"} 
[2025-07-28 14:16:03] local.ERROR: PDF合併任務失敗 {"task_id":"merge_PrdRkuumQRrNXcAQ_1753683361","error":"沒有找到任何可壓縮的PDF檔案","trace":"#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Console\\Commands\\TestPdfCompressionCommand.php(229): App\\Jobs\\ProcessPdfMergeJob->handle()
#1 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Console\\Commands\\TestPdfCompressionCommand.php(89): App\\Console\\Commands\\TestPdfCompressionCommand->executeCompressionTest('2', '114')
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Console\\Commands\\TestPdfCompressionCommand->handle()
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(App\\Console\\Commands\\TestPdfCompressionCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#16 {main}"} 
[2025-07-28 14:21:58] local.INFO: 開始同步招生期間資料 {"api_url":"http://localhost:18001/index.php/api/v1/recommendation_system/sync_exam_period"} 
[2025-07-28 14:22:14] local.INFO: 招生期間資料已保存 {"periods_count":10,"data":[{"exam_id":"2","exam_name":"碩士班甄試招生(測試)","app_date1_start":"2025/07/01 09:00:00","app_date1_end":"2025/08/30 17:00:00","synced_at":"2025-07-28T06:22:13.817850Z"},{"exam_id":"K","exam_name":"技優甄審指定項目甄審報名(測試)","app_date1_start":"2025/06/05 10:00:00","app_date1_end":"2025/06/10 17:00:00","synced_at":"2025-07-28T06:22:13.818525Z"},{"exam_id":"E","exam_name":"單獨招收身心障礙學生考試(測試)","app_date1_start":"2025/04/28 09:00:00","app_date1_end":"2025/05/09 17:00:00","synced_at":"2025-07-28T06:22:13.818676Z"},{"exam_id":"J","exam_name":"新住民入學招生(測試)","app_date1_start":"2025/04/21 09:00:00","app_date1_end":"2025/05/02 17:00:00","synced_at":"2025-07-28T06:22:13.818817Z"},{"exam_id":"7","exam_name":"大學申請入學指定項目甄試(測試)","app_date1_start":"2025/04/08 10:00:00","app_date1_end":"2025/05/01 17:00:00","synced_at":"2025-07-28T06:22:13.818938Z"},{"exam_id":"1","exam_name":"博士班考試招生(測試)","app_date1_start":"2025/03/18 09:00:00","app_date1_end":"2025/04/08 17:00:00","synced_at":"2025-07-28T06:22:13.819046Z"},{"exam_id":"P","exam_name":"碩士在職專班招生(測試)","app_date1_start":"2025/02/18 09:00:00","app_date1_end":"2025/03/25 17:00:00","synced_at":"2025-07-28T06:22:13.819243Z"},{"exam_id":"3","exam_name":"碩士班考試招生(測試)","app_date1_start":"2025/01/03 09:00:00","app_date1_end":"2025/01/22 17:00:00","synced_at":"2025-07-28T06:22:13.819352Z"},{"exam_id":"B","exam_name":"原住民專班招生(測試)","app_date1_start":"2024/12/03 09:00:00","app_date1_end":"2025/02/27 17:00:00","synced_at":"2025-07-28T06:22:13.819456Z"},{"exam_id":"I","exam_name":"特殊選才單獨招生(測試)","app_date1_start":"2024/10/25 09:00:00","app_date1_end":"2024/11/11 17:00:00","synced_at":"2025-07-28T06:22:13.819559Z"}]} 
[2025-07-28 14:22:15] local.INFO: 系統初始化時成功同步外部資料 {"success":true,"message":"系統設定同步完成","results":{"exam_periods":{"success":true,"message":"招生期間資料同步成功","data":{"updated_count":10,"processed_periods":[{"exam_id":"2","exam_name":"碩士班甄試招生(測試)","app_date1_start":"2025/07/01 09:00:00","app_date1_end":"2025/08/30 17:00:00","synced_at":"2025-07-28T06:22:13.817850Z"},{"exam_id":"K","exam_name":"技優甄審指定項目甄審報名(測試)","app_date1_start":"2025/06/05 10:00:00","app_date1_end":"2025/06/10 17:00:00","synced_at":"2025-07-28T06:22:13.818525Z"},{"exam_id":"E","exam_name":"單獨招收身心障礙學生考試(測試)","app_date1_start":"2025/04/28 09:00:00","app_date1_end":"2025/05/09 17:00:00","synced_at":"2025-07-28T06:22:13.818676Z"},{"exam_id":"J","exam_name":"新住民入學招生(測試)","app_date1_start":"2025/04/21 09:00:00","app_date1_end":"2025/05/02 17:00:00","synced_at":"2025-07-28T06:22:13.818817Z"},{"exam_id":"7","exam_name":"大學申請入學指定項目甄試(測試)","app_date1_start":"2025/04/08 10:00:00","app_date1_end":"2025/05/01 17:00:00","synced_at":"2025-07-28T06:22:13.818938Z"},{"exam_id":"1","exam_name":"博士班考試招生(測試)","app_date1_start":"2025/03/18 09:00:00","app_date1_end":"2025/04/08 17:00:00","synced_at":"2025-07-28T06:22:13.819046Z"},{"exam_id":"P","exam_name":"碩士在職專班招生(測試)","app_date1_start":"2025/02/18 09:00:00","app_date1_end":"2025/03/25 17:00:00","synced_at":"2025-07-28T06:22:13.819243Z"},{"exam_id":"3","exam_name":"碩士班考試招生(測試)","app_date1_start":"2025/01/03 09:00:00","app_date1_end":"2025/01/22 17:00:00","synced_at":"2025-07-28T06:22:13.819352Z"},{"exam_id":"B","exam_name":"原住民專班招生(測試)","app_date1_start":"2024/12/03 09:00:00","app_date1_end":"2025/02/27 17:00:00","synced_at":"2025-07-28T06:22:13.819456Z"},{"exam_id":"I","exam_name":"特殊選才單獨招生(測試)","app_date1_start":"2024/10/25 09:00:00","app_date1_end":"2024/11/11 17:00:00","synced_at":"2025-07-28T06:22:13.819559Z"}]}}}} 
[2025-07-28 14:22:26] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/auth-from-external","method":"GET"} 
[2025-07-28 14:22:32] local.INFO: 建立新使用者帳號 {"user_id":2,"email":"<EMAIL>","role":"applicant"} 
[2025-07-28 14:22:32] local.INFO: 建立新考生記錄 {"user_id":2,"applicant_id":1,"external_uid":"ZmOnRv1mNU6+zsms5A1fCg==","exam_year":"114","exam_id":"2"} 
[2025-07-28 14:22:32] local.INFO: 考生eapapi登入成功 {"external_uid":"ZmOnRv1mNU6+zsms5A1fCg==","exam_year":114,"exam_id":"2","user_id":2,"applicant_id":1,"ip":"127.0.0.1"} 
[2025-07-28 14:22:44] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-28T06:22:44.674250Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-28 14:22:44] local.INFO: 【Middleware】考生存取檢查中間件觸發 {"user":2} 
[2025-07-28 14:22:44] local.INFO: 考生嘗試建立推薦函: {"user_id":2,"applicant_id":1,"request_data":{"recommender_email":"<EMAIL>","recommender_name":"吳宗霖","recommender_title":null,"recommender_phone":null,"recommender_department":null,"department_name":"機械工程學系碩士班","program_type":"碩士班甄試招生(測試)","external_autono":348}} 
[2025-07-28 14:22:44] local.INFO: 建立新的推薦人使用者帳號 {"user_id":3,"email":"<EMAIL>","name":"吳宗霖"} 
[2025-07-28 14:22:44] local.INFO: 嘗試新增推薦人資料 {"user_id":3,"email":"<EMAIL>","name":"吳宗霖","title":"","department":"","phone":"","login_token":"尚未生成","exam_year":114,"exam_id":"2"} 
[2025-07-28 14:22:44] local.DEBUG: Generated secure login token {"token_length":64,"timestamp":1753683764} 
[2025-07-28 14:22:44] local.INFO: 建立新的推薦人資料 {"recommender_id":1,"user_id":3,"email":"<EMAIL>","exam_year":114,"exam_id":"2"} 
[2025-07-28 14:22:45] local.INFO: 外部 API 郵件發送成功 {"recipient":"<EMAIL>","subject":"推薦函邀請 – 碩士班甄試招生(測試)（機械工程學系碩士班）","api_response":"{\"status\":\"success\",\"detail\":\"Message has been sent\"}"} 
[2025-07-28 14:22:45] local.INFO: 推薦函邀請信發送成功 {"recommendation_id":1,"recommender_email":"<EMAIL>","email_log_id":1} 
[2025-07-28 14:22:50] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-28T06:22:50.346276Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-28 14:22:50] local.INFO: 【Middleware】考生存取檢查中間件觸發 {"user":2} 
[2025-07-28 14:22:50] local.INFO: 考生嘗試建立推薦函: {"user_id":2,"applicant_id":1,"request_data":{"recommender_email":"<EMAIL>","recommender_name":"Mike","recommender_title":null,"recommender_phone":null,"recommender_department":null,"department_name":"資訊工程學系碩士班","program_type":"碩士班甄試招生(測試)","external_autono":349}} 
[2025-07-28 14:22:50] local.INFO: 建立新的推薦人使用者帳號 {"user_id":4,"email":"<EMAIL>","name":"Mike"} 
[2025-07-28 14:22:50] local.INFO: 嘗試新增推薦人資料 {"user_id":4,"email":"<EMAIL>","name":"Mike","title":"","department":"","phone":"","login_token":"尚未生成","exam_year":114,"exam_id":"2"} 
[2025-07-28 14:22:50] local.DEBUG: Generated secure login token {"token_length":64,"timestamp":1753683770} 
[2025-07-28 14:22:50] local.INFO: 建立新的推薦人資料 {"recommender_id":2,"user_id":4,"email":"<EMAIL>","exam_year":114,"exam_id":"2"} 
[2025-07-28 14:22:50] local.INFO: 外部 API 郵件發送成功 {"recipient":"<EMAIL>","subject":"推薦函邀請 – 碩士班甄試招生(測試)（資訊工程學系碩士班）","api_response":"{\"status\":\"success\",\"detail\":\"Message has been sent\"}"} 
[2025-07-28 14:22:50] local.INFO: 推薦函邀請信發送成功 {"recommendation_id":2,"recommender_email":"<EMAIL>","email_log_id":2} 
[2025-07-28 14:23:29] local.INFO: 推薦人登入成功 {"recommender_id":2,"user_id":4,"token":"67f5d97837638a139f1442dcbbff621ff46e10064b6d044569171bad2fa9ddc6","ip":"127.0.0.1"} 
[2025-07-28 14:23:44] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-28T06:23:44.401077Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-28 14:23:44] local.INFO: 【Middleware】推薦人存取檢查中間件觸發 {"user":4} 
[2025-07-28 14:23:44] local.INFO: PDF檔案已存儲 {"recommendation_id":2,"file_path":"recommendations/2/114/349/2.pdf","file_size":488789,"original_name":"Group 1.pdf","is_overwrite":false,"recommender_id":2} 
[2025-07-28 14:23:44] local.INFO: 推薦函 PDF 上傳成功 {"recommendation_id":"2","file_path":"recommendations/2/114/349/2.pdf","file_size":488789} 
[2025-07-28 14:23:44] local.INFO: 外部 API 郵件發送成功 {"recipient":"<EMAIL>","subject":"推薦函已提交通知 – 碩士班甄試招生(測試)（資訊工程學系碩士班）","api_response":"{\"status\":\"success\",\"detail\":\"Message has been sent\"}"} 
[2025-07-28 14:23:44] local.INFO: 推薦函提交通知信發送成功 {"recommendation_id":2,"applicant_email":"<EMAIL>","email_log_id":3} 
[2025-07-28 14:23:44] local.INFO: 推薦函提交成功 {"recommendation_id":"2","submission_type":"pdf","user_id":4} 
[2025-07-28 14:23:46] local.INFO: 使用者登出 {"user_type":"admin","ip":"127.0.0.1"} 
[2025-07-28 14:24:59] local.INFO: 推薦人登入成功 {"recommender_id":1,"user_id":3,"token":"80663c61361c64697e9e14943423a65ece9728944b50dc741595f66438031477","ip":"127.0.0.1"} 
[2025-07-28 14:25:10] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-28T06:25:10.712045Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-28 14:25:10] local.INFO: 【Middleware】推薦人存取檢查中間件觸發 {"user":3} 
[2025-07-28 14:25:10] local.INFO: PDF檔案已存儲 {"recommendation_id":1,"file_path":"recommendations/2/114/348/1.pdf","file_size":47140,"original_name":"下學期課表.pdf","is_overwrite":true,"recommender_id":1} 
[2025-07-28 14:25:10] local.INFO: 推薦函 PDF 上傳成功 {"recommendation_id":"1","file_path":"recommendations/2/114/348/1.pdf","file_size":47140} 
[2025-07-28 14:25:11] local.INFO: 外部 API 郵件發送成功 {"recipient":"<EMAIL>","subject":"推薦函已提交通知 – 碩士班甄試招生(測試)（機械工程學系碩士班）","api_response":"{\"status\":\"success\",\"detail\":\"Message has been sent\"}"} 
[2025-07-28 14:25:11] local.INFO: 推薦函提交通知信發送成功 {"recommendation_id":1,"applicant_email":"<EMAIL>","email_log_id":4} 
[2025-07-28 14:25:11] local.INFO: 推薦函提交成功 {"recommendation_id":"1","submission_type":"pdf","user_id":3} 
[2025-07-28 14:25:12] local.ERROR: PDF預覽失敗 {"user_id":3,"file_path":"recommendations/2/114/348/1.pdf","error":"無權限訪問此檔案"} 
[2025-07-28 14:25:31] local.INFO: 開始處理PDF壓縮任務 {"task_id":"merge_gk12MwW9efV0MwBv_1753683931","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T06:25:31.500859Z","client_ip":"127.0.0.1","created_by":"test_command","test_mode":true}} 
[2025-07-28 14:25:31] local.WARNING: PDF檔案不存在 {"recommendation_id":1,"pdf_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/recommendations/2/114/348/1.pdf"} 
[2025-07-28 14:25:31] local.WARNING: PDF檔案不存在 {"recommendation_id":2,"pdf_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/recommendations/2/114/349/2.pdf"} 
[2025-07-28 14:25:31] local.ERROR: PDF合併任務失敗 {"task_id":"merge_gk12MwW9efV0MwBv_1753683931","error":"沒有找到任何可壓縮的PDF檔案","trace":"#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Console\\Commands\\TestPdfCompressionCommand.php(229): App\\Jobs\\ProcessPdfMergeJob->handle()
#1 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Console\\Commands\\TestPdfCompressionCommand.php(89): App\\Console\\Commands\\TestPdfCompressionCommand->executeCompressionTest('2', '114')
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Console\\Commands\\TestPdfCompressionCommand->handle()
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(App\\Console\\Commands\\TestPdfCompressionCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#16 {main}"} 
[2025-07-28 14:40:37] local.INFO: 開始處理PDF壓縮任務 {"task_id":"merge_g0cubhzt6UaPU8P8_1753684837","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T06:40:37.262514Z","client_ip":"127.0.0.1","created_by":"test_command","test_mode":true}} 
[2025-07-28 14:40:38] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1.pdf","zip_path":"2/114/348.pdf","file_size":47140} 
[2025-07-28 14:40:38] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/349/2.pdf","zip_path":"2/114/348_2.pdf","file_size":488789} 
[2025-07-28 14:40:38] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-28 14:40:38] local.INFO: ZIP檔案創建成功 {"task_id":"merge_g0cubhzt6UaPU8P8_1753684837","zip_path":"pdf_merges/merged_recommendations_exam_2_year_114_2025-07-28_14-40-38_merge_g0cubhzt6UaPU8P8_1753684837.zip","full_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/public\\pdf_merges/merged_recommendations_exam_2_year_114_2025-07-28_14-40-38_merge_g0cubhzt6UaPU8P8_1753684837.zip","file_count":2,"file_size":499858} 
[2025-07-28 14:40:38] local.INFO: PDF壓縮任務完成 {"task_id":"merge_g0cubhzt6UaPU8P8_1753684837","compressed_files":2,"zip_file":"pdf_merges/merged_recommendations_exam_2_year_114_2025-07-28_14-40-38_merge_g0cubhzt6UaPU8P8_1753684837.zip"} 
[2025-07-28 14:50:02] local.INFO: 開始處理PDF壓縮任務 {"task_id":"merge_mJpz3qwIH2BomHVj_1753685402","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T06:50:02.897850Z","client_ip":"127.0.0.1","created_by":"test_command","test_mode":true}} 
[2025-07-28 14:50:02] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1.pdf","zip_path":"348/1.pdf","file_size":47140} 
[2025-07-28 14:50:02] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/349/2.pdf","zip_path":"348/2.pdf","file_size":488789} 
[2025-07-28 14:50:02] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-28 14:50:03] local.INFO: ZIP檔案創建成功 {"task_id":"merge_mJpz3qwIH2BomHVj_1753685402","zip_path":"pdf_merges/recommendations_2_114_2025-07-28_14-50-02.zip","full_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/public\\pdf_merges/recommendations_2_114_2025-07-28_14-50-02.zip","file_count":2,"file_size":499711} 
[2025-07-28 14:50:03] local.INFO: PDF壓縮任務完成 {"task_id":"merge_mJpz3qwIH2BomHVj_1753685402","compressed_files":2,"zip_file":"pdf_merges/recommendations_2_114_2025-07-28_14-50-02.zip"} 
[2025-07-28 15:02:13] local.INFO: 開始處理PDF壓縮任務 {"task_id":"merge_I9MAt9lzZzbDxGrh_1753686133","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T07:02:13.708093Z","client_ip":"127.0.0.1","created_by":"test_command","test_mode":true}} 
[2025-07-28 15:02:13] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1.pdf","zip_path":"348/1.pdf","file_size":47140} 
[2025-07-28 15:02:13] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/349/2.pdf","zip_path":"348/2.pdf","file_size":488789} 
[2025-07-28 15:02:13] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-28 15:02:13] local.INFO: ZIP檔案創建成功 {"task_id":"merge_I9MAt9lzZzbDxGrh_1753686133","zip_path":"pdf_merges/recommendations_2_114_2025-07-28_15-02-13.zip","full_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/public\\pdf_merges/recommendations_2_114_2025-07-28_15-02-13.zip","file_count":2,"file_size":499711} 
[2025-07-28 15:02:13] local.INFO: PDF壓縮任務完成 {"task_id":"merge_I9MAt9lzZzbDxGrh_1753686133","compressed_files":2,"zip_file":"pdf_merges/recommendations_2_114_2025-07-28_15-02-13.zip"} 
[2025-07-28 15:07:19] local.INFO: 開始處理PDF壓縮任務 {"task_id":"merge_5tvLsvYjzQ6Lnpc4_1753686439","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T07:07:19.074155Z","client_ip":"127.0.0.1","created_by":"test_command","test_mode":true}} 
[2025-07-28 15:07:19] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1.pdf","zip_path":"348/1.pdf","file_size":47140} 
[2025-07-28 15:07:19] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/349/2.pdf","zip_path":"348/2.pdf","file_size":488789} 
[2025-07-28 15:07:19] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-28 15:07:19] local.INFO: ZIP檔案創建成功 {"task_id":"merge_5tvLsvYjzQ6Lnpc4_1753686439","zip_path":"pdf_merges/recommendations_2_114_2025-07-28_15-07-19.zip","full_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/public\\pdf_merges/recommendations_2_114_2025-07-28_15-07-19.zip","file_count":2,"file_size":499711} 
[2025-07-28 15:07:19] local.INFO: PDF壓縮任務完成 {"task_id":"merge_5tvLsvYjzQ6Lnpc4_1753686439","compressed_files":2,"zip_file":"pdf_merges/recommendations_2_114_2025-07-28_15-07-19.zip"} 
[2025-07-28 15:08:52] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/pdf-merge/download","method":"GET"} 
[2025-07-28 15:20:17] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-28 15:20:17] local.INFO: PDF合併任務已啟動 {"task_id":"merge_y8VPCMCfQOorXj9w_1753687217","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T07:20:17.687206Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-28 15:20:23] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:20:28] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:20:34] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:20:39] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:20:45] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:21:57] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-28T07:21:57.874442Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-28 15:21:57] local.INFO: 招生期間檢查 {"exam_id":"K","check_time":"2025-07-28T07:21:57.874442Z","start_time":"2025-06-05T02:00:00.000000Z","end_time":"2025-06-10T09:00:00.000000Z","is_in_period":false} 
[2025-07-28 15:21:57] local.INFO: 招生期間檢查 {"exam_id":"E","check_time":"2025-07-28T07:21:57.874442Z","start_time":"2025-04-28T01:00:00.000000Z","end_time":"2025-05-09T09:00:00.000000Z","is_in_period":false} 
[2025-07-28 15:21:57] local.INFO: 招生期間檢查 {"exam_id":"J","check_time":"2025-07-28T07:21:57.874442Z","start_time":"2025-04-21T01:00:00.000000Z","end_time":"2025-05-02T09:00:00.000000Z","is_in_period":false} 
[2025-07-28 15:21:57] local.INFO: 招生期間檢查 {"exam_id":"7","check_time":"2025-07-28T07:21:57.874442Z","start_time":"2025-04-08T02:00:00.000000Z","end_time":"2025-05-01T09:00:00.000000Z","is_in_period":false} 
[2025-07-28 15:21:57] local.INFO: 招生期間檢查 {"exam_id":"1","check_time":"2025-07-28T07:21:57.874442Z","start_time":"2025-03-18T01:00:00.000000Z","end_time":"2025-04-08T09:00:00.000000Z","is_in_period":false} 
[2025-07-28 15:21:57] local.INFO: 招生期間檢查 {"exam_id":"P","check_time":"2025-07-28T07:21:57.874442Z","start_time":"2025-02-18T01:00:00.000000Z","end_time":"2025-03-25T09:00:00.000000Z","is_in_period":false} 
[2025-07-28 15:21:57] local.INFO: 招生期間檢查 {"exam_id":"3","check_time":"2025-07-28T07:21:57.874442Z","start_time":"2025-01-03T01:00:00.000000Z","end_time":"2025-01-22T09:00:00.000000Z","is_in_period":false} 
[2025-07-28 15:21:57] local.INFO: 招生期間檢查 {"exam_id":"B","check_time":"2025-07-28T07:21:57.874442Z","start_time":"2024-12-03T01:00:00.000000Z","end_time":"2025-02-27T09:00:00.000000Z","is_in_period":false} 
[2025-07-28 15:21:57] local.INFO: 招生期間檢查 {"exam_id":"I","check_time":"2025-07-28T07:21:57.874442Z","start_time":"2024-10-25T01:00:00.000000Z","end_time":"2024-11-11T09:00:00.000000Z","is_in_period":false} 
[2025-07-28 15:26:16] local.INFO: 開始處理PDF壓縮任務 {"task_id":"merge_y8VPCMCfQOorXj9w_1753687217","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T07:20:17.687206Z","client_ip":"127.0.0.1"}} 
[2025-07-28 15:26:17] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1.pdf","zip_path":"348/1.pdf","file_size":47140} 
[2025-07-28 15:26:17] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/349/2.pdf","zip_path":"348/2.pdf","file_size":488789} 
[2025-07-28 15:26:17] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-28 15:26:17] local.INFO: ZIP檔案創建成功 {"task_id":"merge_y8VPCMCfQOorXj9w_1753687217","zip_path":"pdf_merges/recommendations_2_114_2025-07-28_15-26-17.zip","full_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/public\\pdf_merges/recommendations_2_114_2025-07-28_15-26-17.zip","file_count":2,"file_size":499711} 
[2025-07-28 15:26:17] local.INFO: PDF壓縮任務完成 {"task_id":"merge_y8VPCMCfQOorXj9w_1753687217","compressed_files":2,"zip_file":"pdf_merges/recommendations_2_114_2025-07-28_15-26-17.zip"} 
[2025-07-28 15:29:32] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-28 15:29:33] local.INFO: PDF合併任務已啟動 {"task_id":"merge_hBRseTZfuX0NDrLQ_1753687773","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T07:29:33.032770Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-28 15:29:34] local.INFO: 開始處理PDF壓縮任務 {"task_id":"merge_hBRseTZfuX0NDrLQ_1753687773","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T07:29:33.032770Z","client_ip":"127.0.0.1"}} 
[2025-07-28 15:29:34] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1.pdf","zip_path":"348/1.pdf","file_size":47140} 
[2025-07-28 15:29:34] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/349/2.pdf","zip_path":"348/2.pdf","file_size":488789} 
[2025-07-28 15:29:34] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-28 15:29:34] local.INFO: ZIP檔案創建成功 {"task_id":"merge_hBRseTZfuX0NDrLQ_1753687773","zip_path":"pdf_merges/recommendations_2_114_2025-07-28_15-29-34.zip","full_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/public\\pdf_merges/recommendations_2_114_2025-07-28_15-29-34.zip","file_count":2,"file_size":499710} 
[2025-07-28 15:29:34] local.INFO: PDF壓縮任務完成 {"task_id":"merge_hBRseTZfuX0NDrLQ_1753687773","compressed_files":2,"zip_file":"pdf_merges/recommendations_2_114_2025-07-28_15-29-34.zip"} 
[2025-07-28 15:29:38] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:29:45] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:29:50] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:29:56] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:30:01] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:30:07] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:30:12] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:30:18] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:30:23] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:30:29] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:33:06] local.ERROR: syntax error, unexpected token "catch", expecting "function" {"userId":1,"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"catch\", expecting \"function\" at C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Controllers\\Api\\PdfMergeApiController.php:202)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\Users\\\\<USER>\\\\D...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Http\\\\Contro...')
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteSignatureParameters.php(46): method_exists('App\\\\Http\\\\Contro...', 'startMerge')
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteSignatureParameters.php(26): Illuminate\\Routing\\RouteSignatureParameters::fromClassMethodString('App\\\\Http\\\\Contro...')
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(545): Illuminate\\Routing\\RouteSignatureParameters::fromAction(Array, Array)
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\tightenco\\ziggy\\src\\Ziggy.php(173): Illuminate\\Routing\\Route->signatureParameters(Array)
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\tightenco\\ziggy\\src\\Ziggy.php(122): Tighten\\Ziggy\\Ziggy->resolveBindings(Array)
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\tightenco\\ziggy\\src\\Ziggy.php(32): Tighten\\Ziggy\\Ziggy->nameKeyedRoutes()
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\HandleInertiaRequests.php(50): Tighten\\Ziggy\\Ziggy->__construct()
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Http\\Middleware\\HandleInertiaRequests->App\\Http\\Middleware\\{closure}()
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Container\\Container->call(Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(180): Illuminate\\Support\\Facades\\Facade::__callStatic('call', Array)
#16 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(135): Inertia\\Response->resolveArrayableProperties(Array, Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(106): Inertia\\Response->resolveProperties(Object(Illuminate\\Http\\Request), Array)
#18 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Inertia\\Response->toResponse(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#20 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#21 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\CheckSystemAccess.php(55): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\CheckSystemAccess->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\CheckUserRole.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\CheckUserRole->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'admin')
#26 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\HandleAppearance.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#42 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#51 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#74 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#75 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#76 C:\\Users\\<USER>\\Desktop\\rec-letter\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#77 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('C:\\\\Users\\\\<USER>\\\\D...')
#78 {main}
"} 

[2025-07-28 15:33:12] local.ERROR: syntax error, unexpected token "catch", expecting "function" {"userId":1,"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"catch\", expecting \"function\" at C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Controllers\\Api\\PdfMergeApiController.php:208)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\Users\\\\<USER>\\\\D...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Http\\\\Contro...')
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteSignatureParameters.php(46): method_exists('App\\\\Http\\\\Contro...', 'startMerge')
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteSignatureParameters.php(26): Illuminate\\Routing\\RouteSignatureParameters::fromClassMethodString('App\\\\Http\\\\Contro...')
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(545): Illuminate\\Routing\\RouteSignatureParameters::fromAction(Array, Array)
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\tightenco\\ziggy\\src\\Ziggy.php(173): Illuminate\\Routing\\Route->signatureParameters(Array)
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\tightenco\\ziggy\\src\\Ziggy.php(122): Tighten\\Ziggy\\Ziggy->resolveBindings(Array)
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\tightenco\\ziggy\\src\\Ziggy.php(32): Tighten\\Ziggy\\Ziggy->nameKeyedRoutes()
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\HandleInertiaRequests.php(50): Tighten\\Ziggy\\Ziggy->__construct()
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Http\\Middleware\\HandleInertiaRequests->App\\Http\\Middleware\\{closure}()
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Container\\Container->call(Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(180): Illuminate\\Support\\Facades\\Facade::__callStatic('call', Array)
#16 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(135): Inertia\\Response->resolveArrayableProperties(Array, Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(106): Inertia\\Response->resolveProperties(Object(Illuminate\\Http\\Request), Array)
#18 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Inertia\\Response->toResponse(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#20 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#21 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\CheckSystemAccess.php(55): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\CheckSystemAccess->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\CheckUserRole.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\CheckUserRole->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'admin')
#26 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\HandleAppearance.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#42 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#51 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#74 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#75 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#76 C:\\Users\\<USER>\\Desktop\\rec-letter\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#77 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('C:\\\\Users\\\\<USER>\\\\D...')
#78 {main}
"} 
[2025-07-28 15:33:12] local.ERROR: syntax error, unexpected token "catch", expecting "function" {"userId":2,"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"catch\", expecting \"function\" at C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Controllers\\Api\\PdfMergeApiController.php:208)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\Users\\\\<USER>\\\\D...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Http\\\\Contro...')
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteSignatureParameters.php(46): method_exists('App\\\\Http\\\\Contro...', 'startMerge')
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteSignatureParameters.php(26): Illuminate\\Routing\\RouteSignatureParameters::fromClassMethodString('App\\\\Http\\\\Contro...')
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(545): Illuminate\\Routing\\RouteSignatureParameters::fromAction(Array, Array)
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\tightenco\\ziggy\\src\\Ziggy.php(173): Illuminate\\Routing\\Route->signatureParameters(Array)
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\tightenco\\ziggy\\src\\Ziggy.php(122): Tighten\\Ziggy\\Ziggy->resolveBindings(Array)
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\tightenco\\ziggy\\src\\Ziggy.php(32): Tighten\\Ziggy\\Ziggy->nameKeyedRoutes()
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\HandleInertiaRequests.php(50): Tighten\\Ziggy\\Ziggy->__construct()
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Http\\Middleware\\HandleInertiaRequests->App\\Http\\Middleware\\{closure}()
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Container\\Container->call(Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(180): Illuminate\\Support\\Facades\\Facade::__callStatic('call', Array)
#16 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(135): Inertia\\Response->resolveArrayableProperties(Array, Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(106): Inertia\\Response->resolveProperties(Object(Illuminate\\Http\\Request), Array)
#18 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Inertia\\Response->toResponse(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#20 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#21 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\CheckSystemAccess.php(55): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\CheckSystemAccess->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\CheckUserAgreement.php(55): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\CheckUserAgreement->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\HandleAppearance.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#42 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#51 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#74 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#75 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#76 C:\\Users\\<USER>\\Desktop\\rec-letter\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#77 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('C:\\\\Users\\\\<USER>\\\\D...')
#78 {main}
"} 
[2025-07-28 15:34:12] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-28 15:34:12] local.INFO: PDF合併任務已啟動 {"task_id":"merge_LHJw0z2Pv80kKifW_1753688052","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T07:34:12.698259Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-28 15:34:18] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:34:18] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"taskId":"merge_LHJw0z2Pv80kKifW_1753688052"},"input":{"taskId":"merge_LHJw0z2Pv80kKifW_1753688052"},"ip":"127.0.0.1"} 
[2025-07-28 15:34:18] local.WARNING: ⚠️ 任務狀態查詢請求未提供 task_id {"query":{"taskId":"merge_LHJw0z2Pv80kKifW_1753688052"},"ip":"127.0.0.1"} 
[2025-07-28 15:34:24] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:34:24] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"taskId":"merge_LHJw0z2Pv80kKifW_1753688052"},"input":{"taskId":"merge_LHJw0z2Pv80kKifW_1753688052"},"ip":"127.0.0.1"} 
[2025-07-28 15:34:24] local.WARNING: ⚠️ 任務狀態查詢請求未提供 task_id {"query":{"taskId":"merge_LHJw0z2Pv80kKifW_1753688052"},"ip":"127.0.0.1"} 
[2025-07-28 15:34:29] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:34:29] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"taskId":"merge_LHJw0z2Pv80kKifW_1753688052"},"input":{"taskId":"merge_LHJw0z2Pv80kKifW_1753688052"},"ip":"127.0.0.1"} 
[2025-07-28 15:34:29] local.WARNING: ⚠️ 任務狀態查詢請求未提供 task_id {"query":{"taskId":"merge_LHJw0z2Pv80kKifW_1753688052"},"ip":"127.0.0.1"} 
[2025-07-28 15:35:23] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-28 15:35:23] local.INFO: PDF合併任務已啟動 {"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T07:35:23.241548Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-28 15:35:29] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:35:29] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"input":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"ip":"127.0.0.1"} 
[2025-07-28 15:35:29] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123","status":"processing","ip":"127.0.0.1"} 
[2025-07-28 15:35:35] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:35:35] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"input":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"ip":"127.0.0.1"} 
[2025-07-28 15:35:35] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123","status":"processing","ip":"127.0.0.1"} 
[2025-07-28 15:35:40] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:35:40] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"input":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"ip":"127.0.0.1"} 
[2025-07-28 15:35:40] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123","status":"processing","ip":"127.0.0.1"} 
[2025-07-28 15:35:46] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:35:46] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"input":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"ip":"127.0.0.1"} 
[2025-07-28 15:35:46] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123","status":"processing","ip":"127.0.0.1"} 
[2025-07-28 15:35:52] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:35:52] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"input":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"ip":"127.0.0.1"} 
[2025-07-28 15:35:52] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123","status":"processing","ip":"127.0.0.1"} 
[2025-07-28 15:35:57] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:35:57] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"input":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"ip":"127.0.0.1"} 
[2025-07-28 15:35:57] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123","status":"processing","ip":"127.0.0.1"} 
[2025-07-28 15:36:03] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:36:03] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"input":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"ip":"127.0.0.1"} 
[2025-07-28 15:36:03] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123","status":"processing","ip":"127.0.0.1"} 
[2025-07-28 15:36:08] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:36:08] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"input":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"ip":"127.0.0.1"} 
[2025-07-28 15:36:08] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123","status":"processing","ip":"127.0.0.1"} 
[2025-07-28 15:36:14] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:36:14] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"input":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"ip":"127.0.0.1"} 
[2025-07-28 15:36:14] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123","status":"processing","ip":"127.0.0.1"} 
[2025-07-28 15:36:20] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:36:20] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"input":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"ip":"127.0.0.1"} 
[2025-07-28 15:36:20] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123","status":"processing","ip":"127.0.0.1"} 
[2025-07-28 15:39:23] local.INFO: 開始處理PDF壓縮任務 {"task_id":"merge_LHJw0z2Pv80kKifW_1753688052","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T07:34:12.698259Z","client_ip":"127.0.0.1"}} 
[2025-07-28 15:39:23] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1.pdf","zip_path":"348/1.pdf","file_size":47140} 
[2025-07-28 15:39:23] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/349/2.pdf","zip_path":"348/2.pdf","file_size":488789} 
[2025-07-28 15:39:23] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-28 15:39:23] local.INFO: ZIP檔案創建成功 {"task_id":"merge_LHJw0z2Pv80kKifW_1753688052","zip_path":"pdf_merges/recommendations_2_114_2025-07-28_15-39-23.zip","full_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/public\\pdf_merges/recommendations_2_114_2025-07-28_15-39-23.zip","file_count":2,"file_size":499709} 
[2025-07-28 15:39:23] local.INFO: PDF壓縮任務完成 {"task_id":"merge_LHJw0z2Pv80kKifW_1753688052","compressed_files":2,"zip_file":"pdf_merges/recommendations_2_114_2025-07-28_15-39-23.zip"} 
[2025-07-28 15:39:23] local.INFO: 開始處理PDF壓縮任務 {"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T07:35:23.241548Z","client_ip":"127.0.0.1"}} 
[2025-07-28 15:39:23] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1.pdf","zip_path":"348/1.pdf","file_size":47140} 
[2025-07-28 15:39:23] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/349/2.pdf","zip_path":"348/2.pdf","file_size":488789} 
[2025-07-28 15:39:23] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-28 15:39:23] local.INFO: ZIP檔案創建成功 {"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123","zip_path":"pdf_merges/recommendations_2_114_2025-07-28_15-39-23.zip","full_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/public\\pdf_merges/recommendations_2_114_2025-07-28_15-39-23.zip","file_count":2,"file_size":499709} 
[2025-07-28 15:39:23] local.INFO: PDF壓縮任務完成 {"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123","compressed_files":2,"zip_file":"pdf_merges/recommendations_2_114_2025-07-28_15-39-23.zip"} 
[2025-07-28 15:41:31] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-28 15:41:31] local.INFO: PDF合併任務已啟動 {"task_id":"merge_mQKeci3uPltVSAtC_1753688491","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T07:41:31.863329Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-28 15:41:37] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:41:37] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"input":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"ip":"127.0.0.1"} 
[2025-07-28 15:41:37] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_mQKeci3uPltVSAtC_1753688491","status":"processing","ip":"127.0.0.1"} 
[2025-07-28 15:41:42] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:41:42] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"input":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"ip":"127.0.0.1"} 
[2025-07-28 15:41:42] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_mQKeci3uPltVSAtC_1753688491","status":"processing","ip":"127.0.0.1"} 
[2025-07-28 15:41:47] local.INFO: 開始處理PDF壓縮任務 {"task_id":"merge_mQKeci3uPltVSAtC_1753688491","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T07:41:31.863329Z","client_ip":"127.0.0.1"}} 
[2025-07-28 15:41:47] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1.pdf","zip_path":"348/1.pdf","file_size":47140} 
[2025-07-28 15:41:47] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/349/2.pdf","zip_path":"348/2.pdf","file_size":488789} 
[2025-07-28 15:41:47] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-28 15:41:47] local.INFO: ZIP檔案創建成功 {"task_id":"merge_mQKeci3uPltVSAtC_1753688491","zip_path":"pdf_merges/recommendations_2_114_2025-07-28_15-41-47.zip","full_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/public\\pdf_merges/recommendations_2_114_2025-07-28_15-41-47.zip","file_count":2,"file_size":499710} 
[2025-07-28 15:41:47] local.INFO: PDF壓縮任務完成 {"task_id":"merge_mQKeci3uPltVSAtC_1753688491","compressed_files":2,"zip_file":"pdf_merges/recommendations_2_114_2025-07-28_15-41-47.zip"} 
[2025-07-28 15:41:48] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:41:48] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"input":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"ip":"127.0.0.1"} 
[2025-07-28 15:41:48] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_mQKeci3uPltVSAtC_1753688491","status":"error","ip":"127.0.0.1"} 
[2025-07-28 15:41:53] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:41:53] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"input":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"ip":"127.0.0.1"} 
[2025-07-28 15:41:53] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_mQKeci3uPltVSAtC_1753688491","status":"error","ip":"127.0.0.1"} 
[2025-07-28 15:41:59] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:41:59] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"input":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"ip":"127.0.0.1"} 
[2025-07-28 15:41:59] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_mQKeci3uPltVSAtC_1753688491","status":"error","ip":"127.0.0.1"} 
[2025-07-28 15:42:04] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:42:04] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"input":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"ip":"127.0.0.1"} 
[2025-07-28 15:42:04] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_mQKeci3uPltVSAtC_1753688491","status":"error","ip":"127.0.0.1"} 
[2025-07-28 15:42:10] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:42:10] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"input":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"ip":"127.0.0.1"} 
[2025-07-28 15:42:10] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_mQKeci3uPltVSAtC_1753688491","status":"error","ip":"127.0.0.1"} 
[2025-07-28 15:42:16] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:42:16] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"input":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"ip":"127.0.0.1"} 
[2025-07-28 15:42:16] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_mQKeci3uPltVSAtC_1753688491","status":"error","ip":"127.0.0.1"} 
[2025-07-28 15:42:21] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:42:21] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"input":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"ip":"127.0.0.1"} 
[2025-07-28 15:42:21] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_mQKeci3uPltVSAtC_1753688491","status":"error","ip":"127.0.0.1"} 
[2025-07-28 15:42:27] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:42:27] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"input":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"ip":"127.0.0.1"} 
[2025-07-28 15:42:27] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_mQKeci3uPltVSAtC_1753688491","status":"error","ip":"127.0.0.1"} 
[2025-07-28 15:54:20] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-28 15:54:20] local.INFO: 🚀 準備派發PDF壓縮Job到Queue {"task_id":"merge_RtHUN9Xo0Z9WPumk_1753689260","queue_connection":"database","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T07:54:20.469028Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-28 15:54:20] local.INFO: ✅ PDF壓縮Job已成功派發到Queue {"task_id":"merge_RtHUN9Xo0Z9WPumk_1753689260","job_id":"unknown","queue_connection":"database","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T07:54:20.469028Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-28 15:54:20] local.INFO: 📋 PDF壓縮任務已啟動 {"task_id":"merge_RtHUN9Xo0Z9WPumk_1753689260","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T07:54:20.469028Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-28 15:54:26] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:54:26] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_RtHUN9Xo0Z9WPumk_1753689260"},"input":{"task_id":"merge_RtHUN9Xo0Z9WPumk_1753689260"},"ip":"127.0.0.1"} 
[2025-07-28 15:54:26] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_RtHUN9Xo0Z9WPumk_1753689260","status":"processing","ip":"127.0.0.1"} 
[2025-07-28 15:54:31] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:54:31] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_RtHUN9Xo0Z9WPumk_1753689260"},"input":{"task_id":"merge_RtHUN9Xo0Z9WPumk_1753689260"},"ip":"127.0.0.1"} 
[2025-07-28 15:54:31] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_RtHUN9Xo0Z9WPumk_1753689260","status":"processing","ip":"127.0.0.1"} 
[2025-07-28 15:55:06] local.INFO: 🚀 PDF壓縮Job開始執行 {"task_id":"merge_RtHUN9Xo0Z9WPumk_1753689260","job_id":6,"queue":"default","attempts":1,"max_tries":3,"timeout":1800,"parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T07:54:20.469028Z","client_ip":"127.0.0.1"},"memory_usage":27262976,"memory_peak":27262976,"timestamp":"2025-07-28T07:55:06.814923Z"} 
[2025-07-28 15:55:06] local.INFO: 📋 開始處理PDF壓縮任務 {"task_id":"merge_RtHUN9Xo0Z9WPumk_1753689260","job_id":6,"task_status":"processing","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T07:54:20.469028Z","client_ip":"127.0.0.1"},"step":"initialization"} 
[2025-07-28 15:55:06] local.INFO: 📂 步驟1: 獲取推薦函資料 {"task_id":"merge_RtHUN9Xo0Z9WPumk_1753689260","step":"get_recommendations","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T07:54:20.469028Z","client_ip":"127.0.0.1"}} 
[2025-07-28 15:55:06] local.INFO: ✅ 成功獲取推薦函 {"task_id":"merge_RtHUN9Xo0Z9WPumk_1753689260","recommendation_count":2,"step":"get_recommendations_success"} 
[2025-07-28 15:55:06] local.INFO: 👥 步驟2: 按考生分組 {"task_id":"merge_RtHUN9Xo0Z9WPumk_1753689260","step":"group_by_applicant"} 
[2025-07-28 15:55:06] local.INFO: ✅ 考生分組完成 {"task_id":"merge_RtHUN9Xo0Z9WPumk_1753689260","applicant_count":1,"step":"group_by_applicant_success"} 
[2025-07-28 15:55:06] local.INFO: 📁 步驟3: 準備檔案列表 {"task_id":"merge_RtHUN9Xo0Z9WPumk_1753689260","step":"prepare_files","applicant_count":1} 
[2025-07-28 15:55:06] local.DEBUG: 🔄 處理考生檔案 {"task_id":"merge_RtHUN9Xo0Z9WPumk_1753689260","applicant_id":1,"recommendation_count":2,"progress":1,"total":1} 
[2025-07-28 15:55:06] local.DEBUG: ✅ 考生檔案準備完成 {"task_id":"merge_RtHUN9Xo0Z9WPumk_1753689260","applicant_id":1,"file_count":2} 
[2025-07-28 15:55:06] local.INFO: 📊 檔案準備統計 {"task_id":"merge_RtHUN9Xo0Z9WPumk_1753689260","total_files":2,"processed_applicants":1,"step":"prepare_files_complete"} 
[2025-07-28 15:55:06] local.INFO: 🗜️ 步驟4: 開始建立ZIP檔案 {"task_id":"merge_RtHUN9Xo0Z9WPumk_1753689260","step":"create_zip","file_count":2,"memory_usage":31457280} 
[2025-07-28 15:55:06] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1.pdf","zip_path":"348/1.pdf","file_size":47140} 
[2025-07-28 15:55:06] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/349/2.pdf","zip_path":"348/2.pdf","file_size":488789} 
[2025-07-28 15:55:06] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-28 15:55:06] local.INFO: ZIP檔案創建成功 {"task_id":"merge_RtHUN9Xo0Z9WPumk_1753689260","zip_path":"pdf_merges/recommendations_2_114_2025-07-28_15-55-06.zip","full_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/public\\pdf_merges/recommendations_2_114_2025-07-28_15-55-06.zip","file_count":2,"file_size":499710} 
[2025-07-28 15:55:06] local.INFO: ✅ ZIP檔案建立完成 {"task_id":"merge_RtHUN9Xo0Z9WPumk_1753689260","zip_file_path":"pdf_merges/recommendations_2_114_2025-07-28_15-55-06.zip","step":"create_zip_success"} 
[2025-07-28 15:55:06] local.INFO: 🔗 步驟5: 生成下載URL {"task_id":"merge_RtHUN9Xo0Z9WPumk_1753689260","step":"generate_download_url"} 
[2025-07-28 15:55:06] local.INFO: ✅ 下載URL生成完成 {"task_id":"merge_RtHUN9Xo0Z9WPumk_1753689260","download_url":"http://rec-letter.test/api/public/pdf-download/merge_RtHUN9Xo0Z9WPumk_1753689260","step":"generate_download_url_success"} 
[2025-07-28 15:55:06] local.INFO: 🏁 步驟6: 標記任務完成 {"task_id":"merge_RtHUN9Xo0Z9WPumk_1753689260","step":"mark_as_ready"} 
[2025-07-28 15:55:06] local.INFO: 🎉 PDF壓縮任務完成 {"task_id":"merge_RtHUN9Xo0Z9WPumk_1753689260","job_id":6,"compressed_files":2,"zip_file":"pdf_merges/recommendations_2_114_2025-07-28_15-55-06.zip","download_url":"http://rec-letter.test/api/public/pdf-download/merge_RtHUN9Xo0Z9WPumk_1753689260","total_execution_time":0.6070618629455566,"memory_peak":31457280,"step":"job_complete"} 
[2025-07-28 16:10:12] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-28 16:10:12] local.INFO: 🚀 準備派發PDF壓縮Job到Queue {"task_id":"merge_RzYgb8ljiEInyw31_1753690212","queue_connection":"database","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T08:10:12.155296Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-28 16:10:12] local.INFO: ✅ PDF壓縮Job已成功派發到Queue {"task_id":"merge_RzYgb8ljiEInyw31_1753690212","job_id":"unknown","queue_connection":"database","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T08:10:12.155296Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-28 16:10:12] local.INFO: 📋 PDF壓縮任務已啟動 {"task_id":"merge_RzYgb8ljiEInyw31_1753690212","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T08:10:12.155296Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-28 16:10:12] local.INFO: 🚀 PDF壓縮Job開始執行 {"task_id":"merge_RzYgb8ljiEInyw31_1753690212","job_id":7,"queue":"pdf","attempts":1,"max_tries":3,"timeout":1800,"parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T08:10:12.155296Z","client_ip":"127.0.0.1"},"memory_usage":27262976,"memory_peak":27262976,"timestamp":"2025-07-28T08:10:12.405964Z"} 
[2025-07-28 16:10:12] local.INFO: 📋 開始處理PDF壓縮任務 {"task_id":"merge_RzYgb8ljiEInyw31_1753690212","job_id":7,"task_status":"processing","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T08:10:12.155296Z","client_ip":"127.0.0.1"},"step":"initialization"} 
[2025-07-28 16:10:12] local.INFO: 📂 步驟1: 獲取推薦函資料 {"task_id":"merge_RzYgb8ljiEInyw31_1753690212","step":"get_recommendations","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T08:10:12.155296Z","client_ip":"127.0.0.1"}} 
[2025-07-28 16:10:12] local.INFO: ✅ 成功獲取推薦函 {"task_id":"merge_RzYgb8ljiEInyw31_1753690212","recommendation_count":2,"step":"get_recommendations_success"} 
[2025-07-28 16:10:12] local.INFO: 👥 步驟2: 按考生分組 {"task_id":"merge_RzYgb8ljiEInyw31_1753690212","step":"group_by_applicant"} 
[2025-07-28 16:10:12] local.INFO: ✅ 考生分組完成 {"task_id":"merge_RzYgb8ljiEInyw31_1753690212","applicant_count":1,"step":"group_by_applicant_success"} 
[2025-07-28 16:10:12] local.INFO: 📁 步驟3: 準備檔案列表 {"task_id":"merge_RzYgb8ljiEInyw31_1753690212","step":"prepare_files","applicant_count":1} 
[2025-07-28 16:10:12] local.DEBUG: 🔄 處理考生檔案 {"task_id":"merge_RzYgb8ljiEInyw31_1753690212","applicant_id":1,"recommendation_count":2,"progress":1,"total":1} 
[2025-07-28 16:10:12] local.DEBUG: ✅ 考生檔案準備完成 {"task_id":"merge_RzYgb8ljiEInyw31_1753690212","applicant_id":1,"file_count":2} 
[2025-07-28 16:10:12] local.INFO: 📊 檔案準備統計 {"task_id":"merge_RzYgb8ljiEInyw31_1753690212","total_files":2,"processed_applicants":1,"step":"prepare_files_complete"} 
[2025-07-28 16:10:12] local.INFO: 🗜️ 步驟4: 開始建立ZIP檔案 {"task_id":"merge_RzYgb8ljiEInyw31_1753690212","step":"create_zip","file_count":2,"memory_usage":31457280} 
[2025-07-28 16:10:12] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1.pdf","zip_path":"348/1.pdf","file_size":47140} 
[2025-07-28 16:10:12] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/349/2.pdf","zip_path":"348/2.pdf","file_size":488789} 
[2025-07-28 16:10:12] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-28 16:10:12] local.INFO: ZIP檔案創建成功 {"task_id":"merge_RzYgb8ljiEInyw31_1753690212","zip_path":"pdf_merges/recommendations_2_114_2025-07-28_16-10-12.zip","full_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/public\\pdf_merges/recommendations_2_114_2025-07-28_16-10-12.zip","file_count":2,"file_size":499710} 
[2025-07-28 16:10:12] local.INFO: ✅ ZIP檔案建立完成 {"task_id":"merge_RzYgb8ljiEInyw31_1753690212","zip_file_path":"pdf_merges/recommendations_2_114_2025-07-28_16-10-12.zip","step":"create_zip_success"} 
[2025-07-28 16:10:12] local.INFO: 🔗 步驟5: 生成下載URL {"task_id":"merge_RzYgb8ljiEInyw31_1753690212","step":"generate_download_url"} 
[2025-07-28 16:10:12] local.INFO: ✅ 下載URL生成完成 {"task_id":"merge_RzYgb8ljiEInyw31_1753690212","download_url":"http://rec-letter.test/api/public/pdf-download/merge_RzYgb8ljiEInyw31_1753690212","step":"generate_download_url_success"} 
[2025-07-28 16:10:12] local.INFO: 🏁 步驟6: 標記任務完成 {"task_id":"merge_RzYgb8ljiEInyw31_1753690212","step":"mark_as_ready"} 
[2025-07-28 16:10:12] local.INFO: 🎉 PDF壓縮任務完成 {"task_id":"merge_RzYgb8ljiEInyw31_1753690212","job_id":7,"compressed_files":2,"zip_file":"pdf_merges/recommendations_2_114_2025-07-28_16-10-12.zip","download_url":"http://rec-letter.test/api/public/pdf-download/merge_RzYgb8ljiEInyw31_1753690212","total_execution_time":0.763901948928833,"memory_peak":31457280,"step":"job_complete"} 
[2025-07-28 16:10:17] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 16:10:17] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_RzYgb8ljiEInyw31_1753690212"},"input":{"task_id":"merge_RzYgb8ljiEInyw31_1753690212"},"ip":"127.0.0.1"} 
[2025-07-28 16:10:17] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_RzYgb8ljiEInyw31_1753690212","status":"error","ip":"127.0.0.1"} 
[2025-07-28 16:10:23] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 16:10:23] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_RzYgb8ljiEInyw31_1753690212"},"input":{"task_id":"merge_RzYgb8ljiEInyw31_1753690212"},"ip":"127.0.0.1"} 
[2025-07-28 16:10:23] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_RzYgb8ljiEInyw31_1753690212","status":"error","ip":"127.0.0.1"} 
[2025-07-28 16:15:31] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-28 16:15:31] local.INFO: 🚀 準備派發PDF壓縮Job到Queue {"task_id":"merge_HFy9XecbhUWNpoZT_1753690531","queue_connection":"database","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T08:15:31.908470Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-28 16:15:31] local.INFO: ✅ PDF壓縮Job已成功派發到Queue {"task_id":"merge_HFy9XecbhUWNpoZT_1753690531","job_id":"unknown","queue_connection":"database","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T08:15:31.908470Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-28 16:15:31] local.INFO: 📋 PDF壓縮任務已啟動 {"task_id":"merge_HFy9XecbhUWNpoZT_1753690531","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T08:15:31.908470Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-28 16:15:35] local.INFO: 🚀 PDF壓縮Job開始執行 {"task_id":"merge_HFy9XecbhUWNpoZT_1753690531","job_id":8,"queue":"pdf","attempts":1,"max_tries":3,"timeout":1800,"parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T08:15:31.908470Z","client_ip":"127.0.0.1"},"memory_usage":27262976,"memory_peak":27262976,"timestamp":"2025-07-28T08:15:35.657390Z"} 
[2025-07-28 16:15:35] local.INFO: 📋 開始處理PDF壓縮任務 {"task_id":"merge_HFy9XecbhUWNpoZT_1753690531","job_id":8,"task_status":"processing","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T08:15:31.908470Z","client_ip":"127.0.0.1"},"step":"initialization"} 
[2025-07-28 16:15:35] local.INFO: 📂 步驟1: 獲取推薦函資料 {"task_id":"merge_HFy9XecbhUWNpoZT_1753690531","step":"get_recommendations","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T08:15:31.908470Z","client_ip":"127.0.0.1"}} 
[2025-07-28 16:15:35] local.INFO: ✅ 成功獲取推薦函 {"task_id":"merge_HFy9XecbhUWNpoZT_1753690531","recommendation_count":2,"step":"get_recommendations_success"} 
[2025-07-28 16:15:35] local.INFO: 👥 步驟2: 按考生分組 {"task_id":"merge_HFy9XecbhUWNpoZT_1753690531","step":"group_by_applicant"} 
[2025-07-28 16:15:35] local.INFO: ✅ 考生分組完成 {"task_id":"merge_HFy9XecbhUWNpoZT_1753690531","applicant_count":1,"step":"group_by_applicant_success"} 
[2025-07-28 16:15:35] local.INFO: 📁 步驟3: 準備檔案列表 {"task_id":"merge_HFy9XecbhUWNpoZT_1753690531","step":"prepare_files","applicant_count":1} 
[2025-07-28 16:15:35] local.DEBUG: 🔄 處理考生檔案 {"task_id":"merge_HFy9XecbhUWNpoZT_1753690531","applicant_id":1,"recommendation_count":2,"progress":1,"total":1} 
[2025-07-28 16:15:35] local.DEBUG: ✅ 考生檔案準備完成 {"task_id":"merge_HFy9XecbhUWNpoZT_1753690531","applicant_id":1,"file_count":2} 
[2025-07-28 16:15:35] local.INFO: 📊 檔案準備統計 {"task_id":"merge_HFy9XecbhUWNpoZT_1753690531","total_files":2,"processed_applicants":1,"step":"prepare_files_complete"} 
[2025-07-28 16:15:35] local.INFO: 🗜️ 步驟4: 開始建立ZIP檔案 {"task_id":"merge_HFy9XecbhUWNpoZT_1753690531","step":"create_zip","file_count":2,"memory_usage":31457280} 
[2025-07-28 16:15:35] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1.pdf","zip_path":"348/1.pdf","file_size":47140} 
[2025-07-28 16:15:35] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/349/2.pdf","zip_path":"348/2.pdf","file_size":488789} 
[2025-07-28 16:15:35] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-28 16:15:35] local.INFO: ZIP檔案創建成功 {"task_id":"merge_HFy9XecbhUWNpoZT_1753690531","zip_path":"pdf_merges/recommendations_2_114_2025-07-28_16-15-35.zip","full_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/public\\pdf_merges/recommendations_2_114_2025-07-28_16-15-35.zip","file_count":2,"file_size":499711} 
[2025-07-28 16:15:35] local.INFO: ✅ ZIP檔案建立完成 {"task_id":"merge_HFy9XecbhUWNpoZT_1753690531","zip_file_path":"pdf_merges/recommendations_2_114_2025-07-28_16-15-35.zip","step":"create_zip_success"} 
[2025-07-28 16:15:35] local.INFO: 🔗 步驟5: 生成下載URL {"task_id":"merge_HFy9XecbhUWNpoZT_1753690531","step":"generate_download_url"} 
[2025-07-28 16:15:35] local.INFO: ✅ 下載URL生成完成 {"task_id":"merge_HFy9XecbhUWNpoZT_1753690531","download_url":"http://rec-letter.test/api/public/pdf-download/merge_HFy9XecbhUWNpoZT_1753690531","step":"generate_download_url_success"} 
[2025-07-28 16:15:35] local.INFO: 🏁 步驟6: 標記任務完成 {"task_id":"merge_HFy9XecbhUWNpoZT_1753690531","step":"mark_as_ready"} 
[2025-07-28 16:15:35] local.INFO: 🎉 PDF壓縮任務完成 {"task_id":"merge_HFy9XecbhUWNpoZT_1753690531","job_id":8,"compressed_files":2,"zip_file":"pdf_merges/recommendations_2_114_2025-07-28_16-15-35.zip","download_url":"http://rec-letter.test/api/public/pdf-download/merge_HFy9XecbhUWNpoZT_1753690531","total_execution_time":0.5681939125061035,"memory_peak":31457280,"step":"job_complete"} 
[2025-07-28 16:15:37] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 16:15:37] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_HFy9XecbhUWNpoZT_1753690531"},"input":{"task_id":"merge_HFy9XecbhUWNpoZT_1753690531"},"ip":"127.0.0.1"} 
[2025-07-28 16:15:37] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_HFy9XecbhUWNpoZT_1753690531","status":"error","message":"檔案不存在或已損壞","ip":"127.0.0.1"} 
[2025-07-28 16:18:16] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-28 16:18:16] local.INFO: 🚀 準備派發PDF壓縮Job到Queue {"task_id":"merge_p6wLOZ2xXS2szmOV_1753690696","queue_connection":"database","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T08:18:16.912134Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-28 16:18:16] local.INFO: ✅ PDF壓縮Job已成功派發到Queue {"task_id":"merge_p6wLOZ2xXS2szmOV_1753690696","job_id":"unknown","queue_connection":"database","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T08:18:16.912134Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-28 16:18:16] local.INFO: 📋 PDF壓縮任務已啟動 {"task_id":"merge_p6wLOZ2xXS2szmOV_1753690696","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T08:18:16.912134Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-28 16:18:19] local.INFO: 🚀 PDF壓縮Job開始執行 {"task_id":"merge_p6wLOZ2xXS2szmOV_1753690696","job_id":9,"queue":"pdf","attempts":1,"max_tries":3,"timeout":1800,"parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T08:18:16.912134Z","client_ip":"127.0.0.1"},"memory_usage":27262976,"memory_peak":27262976,"timestamp":"2025-07-28T08:18:19.102177Z"} 
[2025-07-28 16:18:19] local.INFO: 📋 開始處理PDF壓縮任務 {"task_id":"merge_p6wLOZ2xXS2szmOV_1753690696","job_id":9,"task_status":"processing","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T08:18:16.912134Z","client_ip":"127.0.0.1"},"step":"initialization"} 
[2025-07-28 16:18:19] local.INFO: 📂 步驟1: 獲取推薦函資料 {"task_id":"merge_p6wLOZ2xXS2szmOV_1753690696","step":"get_recommendations","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T08:18:16.912134Z","client_ip":"127.0.0.1"}} 
[2025-07-28 16:18:19] local.INFO: ✅ 成功獲取推薦函 {"task_id":"merge_p6wLOZ2xXS2szmOV_1753690696","recommendation_count":2,"step":"get_recommendations_success"} 
[2025-07-28 16:18:19] local.INFO: 👥 步驟2: 按考生分組 {"task_id":"merge_p6wLOZ2xXS2szmOV_1753690696","step":"group_by_applicant"} 
[2025-07-28 16:18:19] local.INFO: ✅ 考生分組完成 {"task_id":"merge_p6wLOZ2xXS2szmOV_1753690696","applicant_count":1,"step":"group_by_applicant_success"} 
[2025-07-28 16:18:19] local.INFO: 📁 步驟3: 準備檔案列表 {"task_id":"merge_p6wLOZ2xXS2szmOV_1753690696","step":"prepare_files","applicant_count":1} 
[2025-07-28 16:18:19] local.DEBUG: 🔄 處理考生檔案 {"task_id":"merge_p6wLOZ2xXS2szmOV_1753690696","applicant_id":1,"recommendation_count":2,"progress":1,"total":1} 
[2025-07-28 16:18:19] local.DEBUG: ✅ 考生檔案準備完成 {"task_id":"merge_p6wLOZ2xXS2szmOV_1753690696","applicant_id":1,"file_count":2} 
[2025-07-28 16:18:19] local.INFO: 📊 檔案準備統計 {"task_id":"merge_p6wLOZ2xXS2szmOV_1753690696","total_files":2,"processed_applicants":1,"step":"prepare_files_complete"} 
[2025-07-28 16:18:19] local.INFO: 🗜️ 步驟4: 開始建立ZIP檔案 {"task_id":"merge_p6wLOZ2xXS2szmOV_1753690696","step":"create_zip","file_count":2,"memory_usage":31457280} 
[2025-07-28 16:18:19] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1.pdf","zip_path":"348/1.pdf","file_size":47140} 
[2025-07-28 16:18:19] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/349/2.pdf","zip_path":"348/2.pdf","file_size":488789} 
[2025-07-28 16:18:19] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-28 16:18:19] local.INFO: ZIP檔案創建成功 {"task_id":"merge_p6wLOZ2xXS2szmOV_1753690696","zip_path":"pdf_merges/recommendations_2_114_2025-07-28_16-18-19.zip","full_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/public\\pdf_merges/recommendations_2_114_2025-07-28_16-18-19.zip","file_count":2,"file_size":499710} 
[2025-07-28 16:18:19] local.INFO: ✅ ZIP檔案建立完成 {"task_id":"merge_p6wLOZ2xXS2szmOV_1753690696","zip_file_path":"pdf_merges/recommendations_2_114_2025-07-28_16-18-19.zip","step":"create_zip_success"} 
[2025-07-28 16:18:19] local.INFO: 🔗 步驟5: 生成下載URL {"task_id":"merge_p6wLOZ2xXS2szmOV_1753690696","step":"generate_download_url"} 
[2025-07-28 16:18:19] local.INFO: ✅ 下載URL生成完成 {"task_id":"merge_p6wLOZ2xXS2szmOV_1753690696","download_url":"http://rec-letter.test/api/public/pdf-download/merge_p6wLOZ2xXS2szmOV_1753690696","step":"generate_download_url_success"} 
[2025-07-28 16:18:19] local.INFO: 🏁 步驟6: 標記任務完成 {"task_id":"merge_p6wLOZ2xXS2szmOV_1753690696","step":"mark_as_ready"} 
[2025-07-28 16:18:19] local.INFO: 🎉 PDF壓縮任務完成 {"task_id":"merge_p6wLOZ2xXS2szmOV_1753690696","job_id":9,"compressed_files":2,"zip_file":"pdf_merges/recommendations_2_114_2025-07-28_16-18-19.zip","download_url":"http://rec-letter.test/api/public/pdf-download/merge_p6wLOZ2xXS2szmOV_1753690696","total_execution_time":0.5719599723815918,"memory_peak":31457280,"step":"job_complete"} 
[2025-07-28 16:18:22] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 16:18:22] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_p6wLOZ2xXS2szmOV_1753690696"},"input":{"task_id":"merge_p6wLOZ2xXS2szmOV_1753690696"},"ip":"127.0.0.1"} 
[2025-07-28 16:18:22] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_p6wLOZ2xXS2szmOV_1753690696","status":"ready","message":"任務完成，可以下載","ip":"127.0.0.1"} 
[2025-07-28 16:18:28] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 16:18:28] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_p6wLOZ2xXS2szmOV_1753690696"},"input":{"task_id":"merge_p6wLOZ2xXS2szmOV_1753690696"},"ip":"127.0.0.1"} 
[2025-07-28 16:18:28] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_p6wLOZ2xXS2szmOV_1753690696","status":"ready","message":"任務完成，可以下載","ip":"127.0.0.1"} 
[2025-07-28 16:18:33] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 16:18:33] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_p6wLOZ2xXS2szmOV_1753690696"},"input":{"task_id":"merge_p6wLOZ2xXS2szmOV_1753690696"},"ip":"127.0.0.1"} 
[2025-07-28 16:18:33] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_p6wLOZ2xXS2szmOV_1753690696","status":"ready","message":"任務完成，可以下載","ip":"127.0.0.1"} 
[2025-07-28 16:18:56] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-28 16:18:56] local.INFO: 🚀 準備派發PDF壓縮Job到Queue {"task_id":"merge_hQi2mkPypoubfiuC_1753690736","queue_connection":"database","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T08:18:56.125121Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-28 16:18:56] local.INFO: ✅ PDF壓縮Job已成功派發到Queue {"task_id":"merge_hQi2mkPypoubfiuC_1753690736","job_id":"unknown","queue_connection":"database","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T08:18:56.125121Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-28 16:18:56] local.INFO: 📋 PDF壓縮任務已啟動 {"task_id":"merge_hQi2mkPypoubfiuC_1753690736","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T08:18:56.125121Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-28 16:18:59] local.INFO: 🚀 PDF壓縮Job開始執行 {"task_id":"merge_hQi2mkPypoubfiuC_1753690736","job_id":10,"queue":"pdf","attempts":1,"max_tries":3,"timeout":1800,"parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T08:18:56.125121Z","client_ip":"127.0.0.1"},"memory_usage":27262976,"memory_peak":27262976,"timestamp":"2025-07-28T08:18:58.994846Z"} 
[2025-07-28 16:18:59] local.INFO: 📋 開始處理PDF壓縮任務 {"task_id":"merge_hQi2mkPypoubfiuC_1753690736","job_id":10,"task_status":"processing","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T08:18:56.125121Z","client_ip":"127.0.0.1"},"step":"initialization"} 
[2025-07-28 16:18:59] local.INFO: 📂 步驟1: 獲取推薦函資料 {"task_id":"merge_hQi2mkPypoubfiuC_1753690736","step":"get_recommendations","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T08:18:56.125121Z","client_ip":"127.0.0.1"}} 
[2025-07-28 16:18:59] local.INFO: ✅ 成功獲取推薦函 {"task_id":"merge_hQi2mkPypoubfiuC_1753690736","recommendation_count":2,"step":"get_recommendations_success"} 
[2025-07-28 16:18:59] local.INFO: 👥 步驟2: 按考生分組 {"task_id":"merge_hQi2mkPypoubfiuC_1753690736","step":"group_by_applicant"} 
[2025-07-28 16:18:59] local.INFO: ✅ 考生分組完成 {"task_id":"merge_hQi2mkPypoubfiuC_1753690736","applicant_count":1,"step":"group_by_applicant_success"} 
[2025-07-28 16:18:59] local.INFO: 📁 步驟3: 準備檔案列表 {"task_id":"merge_hQi2mkPypoubfiuC_1753690736","step":"prepare_files","applicant_count":1} 
[2025-07-28 16:18:59] local.DEBUG: 🔄 處理考生檔案 {"task_id":"merge_hQi2mkPypoubfiuC_1753690736","applicant_id":1,"recommendation_count":2,"progress":1,"total":1} 
[2025-07-28 16:18:59] local.DEBUG: ✅ 考生檔案準備完成 {"task_id":"merge_hQi2mkPypoubfiuC_1753690736","applicant_id":1,"file_count":2} 
[2025-07-28 16:18:59] local.INFO: 📊 檔案準備統計 {"task_id":"merge_hQi2mkPypoubfiuC_1753690736","total_files":2,"processed_applicants":1,"step":"prepare_files_complete"} 
[2025-07-28 16:18:59] local.INFO: 🗜️ 步驟4: 開始建立ZIP檔案 {"task_id":"merge_hQi2mkPypoubfiuC_1753690736","step":"create_zip","file_count":2,"memory_usage":31457280} 
[2025-07-28 16:18:59] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1.pdf","zip_path":"348/1.pdf","file_size":47140} 
[2025-07-28 16:18:59] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/349/2.pdf","zip_path":"348/2.pdf","file_size":488789} 
[2025-07-28 16:18:59] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-28 16:18:59] local.INFO: ZIP檔案創建成功 {"task_id":"merge_hQi2mkPypoubfiuC_1753690736","zip_path":"pdf_merges/recommendations_2_114_2025-07-28_16-18-59.zip","full_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/public\\pdf_merges/recommendations_2_114_2025-07-28_16-18-59.zip","file_count":2,"file_size":499711} 
[2025-07-28 16:18:59] local.INFO: ✅ ZIP檔案建立完成 {"task_id":"merge_hQi2mkPypoubfiuC_1753690736","zip_file_path":"pdf_merges/recommendations_2_114_2025-07-28_16-18-59.zip","step":"create_zip_success"} 
[2025-07-28 16:18:59] local.INFO: 🔗 步驟5: 生成下載URL {"task_id":"merge_hQi2mkPypoubfiuC_1753690736","step":"generate_download_url"} 
[2025-07-28 16:18:59] local.INFO: ✅ 下載URL生成完成 {"task_id":"merge_hQi2mkPypoubfiuC_1753690736","download_url":"http://rec-letter.test/api/public/pdf-download/merge_hQi2mkPypoubfiuC_1753690736","step":"generate_download_url_success"} 
[2025-07-28 16:18:59] local.INFO: 🏁 步驟6: 標記任務完成 {"task_id":"merge_hQi2mkPypoubfiuC_1753690736","step":"mark_as_ready"} 
[2025-07-28 16:18:59] local.INFO: 🎉 PDF壓縮任務完成 {"task_id":"merge_hQi2mkPypoubfiuC_1753690736","job_id":10,"compressed_files":2,"zip_file":"pdf_merges/recommendations_2_114_2025-07-28_16-18-59.zip","download_url":"http://rec-letter.test/api/public/pdf-download/merge_hQi2mkPypoubfiuC_1753690736","total_execution_time":0.7211160659790039,"memory_peak":31457280,"step":"job_complete"} 
[2025-07-28 16:19:01] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 16:19:01] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_hQi2mkPypoubfiuC_1753690736"},"input":{"task_id":"merge_hQi2mkPypoubfiuC_1753690736"},"ip":"127.0.0.1"} 
[2025-07-28 16:19:01] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_hQi2mkPypoubfiuC_1753690736","status":"ready","message":"任務完成，可以下載","ip":"127.0.0.1"} 
[2025-07-28 16:19:02] local.INFO: 公共PDF壓縮檔案下載 {"task_id":"merge_hQi2mkPypoubfiuC_1753690736","file_path":"pdf_merges/recommendations_2_114_2025-07-28_16-18-59.zip","client_ip":"127.0.0.1","user_agent":"GuzzleHttp/7"} 
[2025-07-28 16:27:12] local.ERROR: SQLSTATE[HY000]: General error: 1 no such table: failed_jobs (Connection: sqlite, SQL: select count(*) as aggregate from "failed_jobs") {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: failed_jobs (Connection: sqlite, SQL: select count(*) as aggregate from \"failed_jobs\") at C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3131): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3630): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3558): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Console\\Commands\\QueueDiagnosticsCommand.php(281): Illuminate\\Database\\Query\\Builder->count()
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Console\\Commands\\QueueDiagnosticsCommand.php(39): App\\Console\\Commands\\QueueDiagnosticsCommand->clearFailedJobs()
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Console\\Commands\\QueueDiagnosticsCommand->handle()
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#16 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#17 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#18 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(App\\Console\\Commands\\QueueDiagnosticsCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 C:\\Users\\<USER>\\Desktop\\rec-letter\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#24 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: failed_jobs at C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select count(*)...')
#1 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select count(*)...', Array)
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3131): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3630): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3558): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Console\\Commands\\QueueDiagnosticsCommand.php(281): Illuminate\\Database\\Query\\Builder->count()
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Console\\Commands\\QueueDiagnosticsCommand.php(39): App\\Console\\Commands\\QueueDiagnosticsCommand->clearFailedJobs()
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Console\\Commands\\QueueDiagnosticsCommand->handle()
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#16 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#17 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#18 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#19 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#20 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(App\\Console\\Commands\\QueueDiagnosticsCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 C:\\Users\\<USER>\\Desktop\\rec-letter\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#26 {main}
"} 
[2025-07-28 16:55:13] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-28 16:55:13] local.INFO: 🚀 準備派發PDF壓縮Job到Queue {"task_id":"merge_t4fMIvQtIlTno2gw_1753692913","queue_connection":"database","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T08:55:13.508979Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-28 16:55:13] local.INFO: ✅ PDF壓縮Job已成功派發到Queue {"task_id":"merge_t4fMIvQtIlTno2gw_1753692913","job_id":"unknown","queue_connection":"database","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T08:55:13.508979Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-28 16:55:13] local.INFO: 📋 PDF壓縮任務已啟動 {"task_id":"merge_t4fMIvQtIlTno2gw_1753692913","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T08:55:13.508979Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-28 16:55:19] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 16:55:19] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_t4fMIvQtIlTno2gw_1753692913"},"input":{"task_id":"merge_t4fMIvQtIlTno2gw_1753692913"},"ip":"127.0.0.1"} 
[2025-07-28 16:55:19] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_t4fMIvQtIlTno2gw_1753692913","status":"processing","message":"任務處理中","ip":"127.0.0.1"} 
[2025-07-28 16:55:24] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 16:55:24] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_t4fMIvQtIlTno2gw_1753692913"},"input":{"task_id":"merge_t4fMIvQtIlTno2gw_1753692913"},"ip":"127.0.0.1"} 
[2025-07-28 16:55:24] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_t4fMIvQtIlTno2gw_1753692913","status":"processing","message":"任務處理中","ip":"127.0.0.1"} 
[2025-07-28 16:55:30] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 16:55:30] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_t4fMIvQtIlTno2gw_1753692913"},"input":{"task_id":"merge_t4fMIvQtIlTno2gw_1753692913"},"ip":"127.0.0.1"} 
[2025-07-28 16:55:30] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_t4fMIvQtIlTno2gw_1753692913","status":"processing","message":"任務處理中","ip":"127.0.0.1"} 
[2025-07-28 16:55:36] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 16:55:36] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_t4fMIvQtIlTno2gw_1753692913"},"input":{"task_id":"merge_t4fMIvQtIlTno2gw_1753692913"},"ip":"127.0.0.1"} 
[2025-07-28 16:55:36] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_t4fMIvQtIlTno2gw_1753692913","status":"processing","message":"任務處理中","ip":"127.0.0.1"} 
[2025-07-28 16:55:41] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 16:55:41] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_t4fMIvQtIlTno2gw_1753692913"},"input":{"task_id":"merge_t4fMIvQtIlTno2gw_1753692913"},"ip":"127.0.0.1"} 
[2025-07-28 16:55:41] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_t4fMIvQtIlTno2gw_1753692913","status":"processing","message":"任務處理中","ip":"127.0.0.1"} 
[2025-07-28 16:55:47] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 16:55:47] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_t4fMIvQtIlTno2gw_1753692913"},"input":{"task_id":"merge_t4fMIvQtIlTno2gw_1753692913"},"ip":"127.0.0.1"} 
[2025-07-28 16:55:47] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_t4fMIvQtIlTno2gw_1753692913","status":"processing","message":"任務處理中","ip":"127.0.0.1"} 
[2025-07-28 16:55:52] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 16:55:52] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_t4fMIvQtIlTno2gw_1753692913"},"input":{"task_id":"merge_t4fMIvQtIlTno2gw_1753692913"},"ip":"127.0.0.1"} 
[2025-07-28 16:55:52] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_t4fMIvQtIlTno2gw_1753692913","status":"processing","message":"任務處理中","ip":"127.0.0.1"} 
[2025-07-28 16:55:58] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 16:55:58] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_t4fMIvQtIlTno2gw_1753692913"},"input":{"task_id":"merge_t4fMIvQtIlTno2gw_1753692913"},"ip":"127.0.0.1"} 
[2025-07-28 16:55:58] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_t4fMIvQtIlTno2gw_1753692913","status":"processing","message":"任務處理中","ip":"127.0.0.1"} 
[2025-07-28 16:56:03] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 16:56:03] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_t4fMIvQtIlTno2gw_1753692913"},"input":{"task_id":"merge_t4fMIvQtIlTno2gw_1753692913"},"ip":"127.0.0.1"} 
[2025-07-28 16:56:03] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_t4fMIvQtIlTno2gw_1753692913","status":"processing","message":"任務處理中","ip":"127.0.0.1"} 
[2025-07-28 16:56:08] local.INFO: 🚀 PDF壓縮Job開始執行 {"task_id":"merge_t4fMIvQtIlTno2gw_1753692913","job_id":11,"queue":"pdf","attempts":1,"max_tries":3,"timeout":1800,"parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T08:55:13.508979Z","client_ip":"127.0.0.1"},"memory_usage":27262976,"memory_peak":27262976,"timestamp":"2025-07-28T08:56:08.627153Z"} 
[2025-07-28 16:56:08] local.INFO: 📋 開始處理PDF壓縮任務 {"task_id":"merge_t4fMIvQtIlTno2gw_1753692913","job_id":11,"task_status":"processing","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T08:55:13.508979Z","client_ip":"127.0.0.1"},"step":"initialization"} 
[2025-07-28 16:56:08] local.INFO: 📂 步驟1: 獲取推薦函資料 {"task_id":"merge_t4fMIvQtIlTno2gw_1753692913","step":"get_recommendations","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T08:55:13.508979Z","client_ip":"127.0.0.1"}} 
[2025-07-28 16:56:08] local.INFO: ✅ 成功獲取推薦函 {"task_id":"merge_t4fMIvQtIlTno2gw_1753692913","recommendation_count":2,"step":"get_recommendations_success"} 
[2025-07-28 16:56:08] local.INFO: 👥 步驟2: 按考生分組 {"task_id":"merge_t4fMIvQtIlTno2gw_1753692913","step":"group_by_applicant"} 
[2025-07-28 16:56:08] local.INFO: ✅ 考生分組完成 {"task_id":"merge_t4fMIvQtIlTno2gw_1753692913","applicant_count":1,"step":"group_by_applicant_success"} 
[2025-07-28 16:56:08] local.INFO: 📁 步驟3: 準備檔案列表 {"task_id":"merge_t4fMIvQtIlTno2gw_1753692913","step":"prepare_files","applicant_count":1} 
[2025-07-28 16:56:08] local.DEBUG: 🔄 處理考生檔案 {"task_id":"merge_t4fMIvQtIlTno2gw_1753692913","applicant_id":1,"recommendation_count":2,"progress":1,"total":1} 
[2025-07-28 16:56:08] local.DEBUG: ✅ 考生檔案準備完成 {"task_id":"merge_t4fMIvQtIlTno2gw_1753692913","applicant_id":1,"file_count":2} 
[2025-07-28 16:56:08] local.INFO: 📊 檔案準備統計 {"task_id":"merge_t4fMIvQtIlTno2gw_1753692913","total_files":2,"processed_applicants":1,"step":"prepare_files_complete"} 
[2025-07-28 16:56:08] local.INFO: 🗜️ 步驟4: 開始建立ZIP檔案 {"task_id":"merge_t4fMIvQtIlTno2gw_1753692913","step":"create_zip","file_count":2,"memory_usage":31457280} 
[2025-07-28 16:56:08] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1.pdf","zip_path":"348/1.pdf","file_size":47140} 
[2025-07-28 16:56:08] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/349/2.pdf","zip_path":"348/2.pdf","file_size":488789} 
[2025-07-28 16:56:08] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-28 16:56:08] local.INFO: ZIP檔案創建成功 {"task_id":"merge_t4fMIvQtIlTno2gw_1753692913","zip_path":"pdf_merges/recommendations_2_114_2025-07-28_16-56-08.zip","full_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/public\\pdf_merges/recommendations_2_114_2025-07-28_16-56-08.zip","file_count":2,"file_size":499710} 
[2025-07-28 16:56:08] local.INFO: ✅ ZIP檔案建立完成 {"task_id":"merge_t4fMIvQtIlTno2gw_1753692913","zip_file_path":"pdf_merges/recommendations_2_114_2025-07-28_16-56-08.zip","step":"create_zip_success"} 
[2025-07-28 16:56:08] local.INFO: 🔗 步驟5: 生成下載URL {"task_id":"merge_t4fMIvQtIlTno2gw_1753692913","step":"generate_download_url"} 
[2025-07-28 16:56:08] local.INFO: ✅ 下載URL生成完成 {"task_id":"merge_t4fMIvQtIlTno2gw_1753692913","download_url":"http://rec-letter.test/api/public/pdf-download/merge_t4fMIvQtIlTno2gw_1753692913","step":"generate_download_url_success"} 
[2025-07-28 16:56:08] local.INFO: 🏁 步驟6: 標記任務完成 {"task_id":"merge_t4fMIvQtIlTno2gw_1753692913","step":"mark_as_ready"} 
[2025-07-28 16:56:08] local.INFO: 🎉 PDF壓縮任務完成 {"task_id":"merge_t4fMIvQtIlTno2gw_1753692913","job_id":11,"compressed_files":2,"zip_file":"pdf_merges/recommendations_2_114_2025-07-28_16-56-08.zip","download_url":"http://rec-letter.test/api/public/pdf-download/merge_t4fMIvQtIlTno2gw_1753692913","total_execution_time":0.5795750617980957,"memory_peak":31457280,"step":"job_complete"} 
[2025-07-28 16:56:09] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 16:56:09] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_t4fMIvQtIlTno2gw_1753692913"},"input":{"task_id":"merge_t4fMIvQtIlTno2gw_1753692913"},"ip":"127.0.0.1"} 
[2025-07-28 16:56:09] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_t4fMIvQtIlTno2gw_1753692913","status":"ready","message":"任務完成，可以下載","ip":"127.0.0.1"} 
[2025-07-28 16:56:09] local.INFO: 公共PDF壓縮檔案下載 {"task_id":"merge_t4fMIvQtIlTno2gw_1753692913","file_path":"pdf_merges/recommendations_2_114_2025-07-28_16-56-08.zip","client_ip":"127.0.0.1","user_agent":"GuzzleHttp/7"} 
[2025-07-29 09:20:35] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-29 09:20:35] local.INFO: 🚀 準備派發PDF壓縮Job到Queue {"task_id":"merge_4ILsrs7dueuKR93U_1753752035","queue_connection":"database","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-29T01:20:35.828774Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-29 09:20:35] local.INFO: ✅ PDF壓縮Job已成功派發到Queue {"task_id":"merge_4ILsrs7dueuKR93U_1753752035","job_id":"unknown","queue_connection":"database","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-29T01:20:35.828774Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-29 09:20:35] local.INFO: 📋 PDF壓縮任務已啟動 {"task_id":"merge_4ILsrs7dueuKR93U_1753752035","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-29T01:20:35.828774Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-29 09:20:38] local.INFO: 🚀 PDF壓縮Job開始執行 {"task_id":"merge_4ILsrs7dueuKR93U_1753752035","job_id":12,"queue":"pdf","attempts":1,"max_tries":3,"timeout":1800,"parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-29T01:20:35.828774Z","client_ip":"127.0.0.1"},"memory_usage":27262976,"memory_peak":27262976,"timestamp":"2025-07-29T01:20:37.392525Z"} 
[2025-07-29 09:20:39] local.INFO: 📋 開始處理PDF壓縮任務 {"task_id":"merge_4ILsrs7dueuKR93U_1753752035","job_id":12,"task_status":"processing","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-29T01:20:35.828774Z","client_ip":"127.0.0.1"},"step":"initialization"} 
[2025-07-29 09:20:39] local.INFO: 📂 步驟1: 獲取推薦函資料 {"task_id":"merge_4ILsrs7dueuKR93U_1753752035","step":"get_recommendations","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-29T01:20:35.828774Z","client_ip":"127.0.0.1"}} 
[2025-07-29 09:20:39] local.INFO: ✅ 成功獲取推薦函 {"task_id":"merge_4ILsrs7dueuKR93U_1753752035","recommendation_count":2,"step":"get_recommendations_success"} 
[2025-07-29 09:20:39] local.INFO: 👥 步驟2: 按考生分組 {"task_id":"merge_4ILsrs7dueuKR93U_1753752035","step":"group_by_applicant"} 
[2025-07-29 09:20:39] local.INFO: ✅ 考生分組完成 {"task_id":"merge_4ILsrs7dueuKR93U_1753752035","applicant_count":1,"step":"group_by_applicant_success"} 
[2025-07-29 09:20:39] local.INFO: 📁 步驟3: 準備檔案列表 {"task_id":"merge_4ILsrs7dueuKR93U_1753752035","step":"prepare_files","applicant_count":1} 
[2025-07-29 09:20:39] local.DEBUG: 🔄 處理考生檔案 {"task_id":"merge_4ILsrs7dueuKR93U_1753752035","applicant_id":1,"recommendation_count":2,"progress":1,"total":1} 
[2025-07-29 09:20:40] local.DEBUG: ✅ 考生檔案準備完成 {"task_id":"merge_4ILsrs7dueuKR93U_1753752035","applicant_id":1,"file_count":2} 
[2025-07-29 09:20:40] local.INFO: 📊 檔案準備統計 {"task_id":"merge_4ILsrs7dueuKR93U_1753752035","total_files":2,"processed_applicants":1,"step":"prepare_files_complete"} 
[2025-07-29 09:20:40] local.INFO: 🗜️ 步驟4: 開始建立ZIP檔案 {"task_id":"merge_4ILsrs7dueuKR93U_1753752035","step":"create_zip","file_count":2,"memory_usage":31457280} 
[2025-07-29 09:20:40] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1.pdf","zip_path":"348/1.pdf","file_size":47140} 
[2025-07-29 09:20:40] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/349/2.pdf","zip_path":"348/2.pdf","file_size":488789} 
[2025-07-29 09:20:40] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-29 09:20:40] local.INFO: ZIP檔案創建成功 {"task_id":"merge_4ILsrs7dueuKR93U_1753752035","zip_path":"pdf_merges/recommendations_2_114_2025-07-29_09-20-40.zip","full_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/public\\pdf_merges/recommendations_2_114_2025-07-29_09-20-40.zip","file_count":2,"file_size":499710} 
[2025-07-29 09:20:40] local.INFO: ✅ ZIP檔案建立完成 {"task_id":"merge_4ILsrs7dueuKR93U_1753752035","zip_file_path":"pdf_merges/recommendations_2_114_2025-07-29_09-20-40.zip","step":"create_zip_success"} 
[2025-07-29 09:20:40] local.INFO: 🔗 步驟5: 生成下載URL {"task_id":"merge_4ILsrs7dueuKR93U_1753752035","step":"generate_download_url"} 
[2025-07-29 09:20:40] local.INFO: ✅ 下載URL生成完成 {"task_id":"merge_4ILsrs7dueuKR93U_1753752035","download_url":"http://rec-letter.test/api/public/pdf-download/merge_4ILsrs7dueuKR93U_1753752035","step":"generate_download_url_success"} 
[2025-07-29 09:20:40] local.INFO: 🏁 步驟6: 標記任務完成 {"task_id":"merge_4ILsrs7dueuKR93U_1753752035","step":"mark_as_ready"} 
[2025-07-29 09:20:40] local.INFO: 🎉 PDF壓縮任務完成 {"task_id":"merge_4ILsrs7dueuKR93U_1753752035","job_id":12,"compressed_files":2,"zip_file":"pdf_merges/recommendations_2_114_2025-07-29_09-20-40.zip","download_url":"http://rec-letter.test/api/public/pdf-download/merge_4ILsrs7dueuKR93U_1753752035","total_execution_time":4.2604851722717285,"memory_peak":31457280,"step":"job_complete"} 
[2025-07-29 09:20:42] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-29 09:20:42] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_4ILsrs7dueuKR93U_1753752035"},"input":{"task_id":"merge_4ILsrs7dueuKR93U_1753752035"},"ip":"127.0.0.1"} 
[2025-07-29 09:20:42] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_4ILsrs7dueuKR93U_1753752035","status":"ready","message":"任務完成，可以下載","ip":"127.0.0.1"} 
[2025-07-29 09:20:42] local.INFO: 公共PDF壓縮檔案下載 {"task_id":"merge_4ILsrs7dueuKR93U_1753752035","file_path":"pdf_merges/recommendations_2_114_2025-07-29_09-20-40.zip","client_ip":"127.0.0.1","user_agent":"GuzzleHttp/7"} 
[2025-07-29 09:25:17] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-29 09:25:17] local.INFO: 🚀 準備派發PDF壓縮Job到Queue {"task_id":"merge_5ECBcZkQOvnCuHAs_1753752317","queue_connection":"database","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-29T01:25:17.107143Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-29 09:25:17] local.INFO: ✅ PDF壓縮Job已成功派發到Queue {"task_id":"merge_5ECBcZkQOvnCuHAs_1753752317","job_id":"unknown","queue_connection":"database","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-29T01:25:17.107143Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-29 09:25:17] local.INFO: 📋 PDF壓縮任務已啟動 {"task_id":"merge_5ECBcZkQOvnCuHAs_1753752317","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-29T01:25:17.107143Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-29 09:25:20] local.INFO: 🚀 PDF壓縮Job開始執行 {"task_id":"merge_5ECBcZkQOvnCuHAs_1753752317","job_id":13,"queue":"pdf","attempts":1,"max_tries":3,"timeout":1800,"parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-29T01:25:17.107143Z","client_ip":"127.0.0.1"},"memory_usage":27262976,"memory_peak":27262976,"timestamp":"2025-07-29T01:25:20.402136Z"} 
[2025-07-29 09:25:20] local.INFO: 📋 開始處理PDF壓縮任務 {"task_id":"merge_5ECBcZkQOvnCuHAs_1753752317","job_id":13,"task_status":"processing","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-29T01:25:17.107143Z","client_ip":"127.0.0.1"},"step":"initialization"} 
[2025-07-29 09:25:20] local.INFO: 📂 步驟1: 獲取推薦函資料 {"task_id":"merge_5ECBcZkQOvnCuHAs_1753752317","step":"get_recommendations","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-29T01:25:17.107143Z","client_ip":"127.0.0.1"}} 
[2025-07-29 09:25:20] local.INFO: ✅ 成功獲取推薦函 {"task_id":"merge_5ECBcZkQOvnCuHAs_1753752317","recommendation_count":2,"step":"get_recommendations_success"} 
[2025-07-29 09:25:20] local.INFO: 👥 步驟2: 按考生分組 {"task_id":"merge_5ECBcZkQOvnCuHAs_1753752317","step":"group_by_applicant"} 
[2025-07-29 09:25:20] local.INFO: ✅ 考生分組完成 {"task_id":"merge_5ECBcZkQOvnCuHAs_1753752317","applicant_count":1,"step":"group_by_applicant_success"} 
[2025-07-29 09:25:20] local.INFO: 📁 步驟3: 準備檔案列表 {"task_id":"merge_5ECBcZkQOvnCuHAs_1753752317","step":"prepare_files","applicant_count":1} 
[2025-07-29 09:25:20] local.DEBUG: 🔄 處理考生檔案 {"task_id":"merge_5ECBcZkQOvnCuHAs_1753752317","applicant_id":1,"recommendation_count":2,"progress":1,"total":1} 
[2025-07-29 09:25:20] local.DEBUG: ✅ 考生檔案準備完成 {"task_id":"merge_5ECBcZkQOvnCuHAs_1753752317","applicant_id":1,"file_count":2} 
[2025-07-29 09:25:20] local.INFO: 📊 檔案準備統計 {"task_id":"merge_5ECBcZkQOvnCuHAs_1753752317","total_files":2,"processed_applicants":1,"step":"prepare_files_complete"} 
[2025-07-29 09:25:20] local.INFO: 🗜️ 步驟4: 開始建立ZIP檔案 {"task_id":"merge_5ECBcZkQOvnCuHAs_1753752317","step":"create_zip","file_count":2,"memory_usage":31457280} 
[2025-07-29 09:25:20] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1.pdf","zip_path":"348/1.pdf","file_size":47140} 
[2025-07-29 09:25:20] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/349/2.pdf","zip_path":"348/2.pdf","file_size":488789} 
[2025-07-29 09:25:20] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-29 09:25:20] local.INFO: ZIP檔案創建成功 {"task_id":"merge_5ECBcZkQOvnCuHAs_1753752317","zip_path":"pdf_merges/recommendations_2_114_2025-07-29_09-25-20.zip","full_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/public\\pdf_merges/recommendations_2_114_2025-07-29_09-25-20.zip","file_count":2,"file_size":499711} 
[2025-07-29 09:25:20] local.INFO: ✅ ZIP檔案建立完成 {"task_id":"merge_5ECBcZkQOvnCuHAs_1753752317","zip_file_path":"pdf_merges/recommendations_2_114_2025-07-29_09-25-20.zip","step":"create_zip_success"} 
[2025-07-29 09:25:20] local.INFO: 🔗 步驟5: 生成下載URL {"task_id":"merge_5ECBcZkQOvnCuHAs_1753752317","step":"generate_download_url"} 
[2025-07-29 09:25:20] local.INFO: ✅ 下載URL生成完成 {"task_id":"merge_5ECBcZkQOvnCuHAs_1753752317","download_url":"http://rec-letter.test/api/public/pdf-download/merge_5ECBcZkQOvnCuHAs_1753752317","step":"generate_download_url_success"} 
[2025-07-29 09:25:20] local.INFO: 🏁 步驟6: 標記任務完成 {"task_id":"merge_5ECBcZkQOvnCuHAs_1753752317","step":"mark_as_ready"} 
[2025-07-29 09:25:20] local.INFO: 🎉 PDF壓縮任務完成 {"task_id":"merge_5ECBcZkQOvnCuHAs_1753752317","job_id":13,"compressed_files":2,"zip_file":"pdf_merges/recommendations_2_114_2025-07-29_09-25-20.zip","download_url":"http://rec-letter.test/api/public/pdf-download/merge_5ECBcZkQOvnCuHAs_1753752317","total_execution_time":0.5601210594177246,"memory_peak":31457280,"step":"job_complete"} 
[2025-07-29 09:25:22] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-29 09:25:22] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_5ECBcZkQOvnCuHAs_1753752317"},"input":{"task_id":"merge_5ECBcZkQOvnCuHAs_1753752317"},"ip":"127.0.0.1"} 
[2025-07-29 09:25:22] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_5ECBcZkQOvnCuHAs_1753752317","status":"ready","message":"任務完成，可以下載","ip":"127.0.0.1"} 
[2025-07-29 09:25:23] local.INFO: 公共PDF壓縮檔案下載 {"task_id":"merge_5ECBcZkQOvnCuHAs_1753752317","file_path":"pdf_merges/recommendations_2_114_2025-07-29_09-25-20.zip","client_ip":"127.0.0.1","user_agent":"GuzzleHttp/7"} 
[2025-07-29 09:28:32] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-29 09:28:32] local.INFO: 🚀 準備派發PDF壓縮Job到Queue {"task_id":"merge_95rK61phPaYCeEge_1753752512","queue_connection":"database","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-29T01:28:32.115347Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-29 09:28:32] local.INFO: ✅ PDF壓縮Job已成功派發到Queue {"task_id":"merge_95rK61phPaYCeEge_1753752512","job_id":"unknown","queue_connection":"database","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-29T01:28:32.115347Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-29 09:28:32] local.INFO: 📋 PDF壓縮任務已啟動 {"task_id":"merge_95rK61phPaYCeEge_1753752512","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-29T01:28:32.115347Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-29 09:28:34] local.INFO: 🚀 PDF壓縮Job開始執行 {"task_id":"merge_95rK61phPaYCeEge_1753752512","job_id":14,"queue":"pdf","attempts":1,"max_tries":3,"timeout":1800,"parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-29T01:28:32.115347Z","client_ip":"127.0.0.1"},"memory_usage":27262976,"memory_peak":27262976,"timestamp":"2025-07-29T01:28:34.852206Z"} 
[2025-07-29 09:28:34] local.INFO: 📋 開始處理PDF壓縮任務 {"task_id":"merge_95rK61phPaYCeEge_1753752512","job_id":14,"task_status":"processing","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-29T01:28:32.115347Z","client_ip":"127.0.0.1"},"step":"initialization"} 
[2025-07-29 09:28:34] local.INFO: 📂 步驟1: 獲取推薦函資料 {"task_id":"merge_95rK61phPaYCeEge_1753752512","step":"get_recommendations","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-29T01:28:32.115347Z","client_ip":"127.0.0.1"}} 
[2025-07-29 09:28:34] local.INFO: ✅ 成功獲取推薦函 {"task_id":"merge_95rK61phPaYCeEge_1753752512","recommendation_count":2,"step":"get_recommendations_success"} 
[2025-07-29 09:28:34] local.INFO: 👥 步驟2: 按考生分組 {"task_id":"merge_95rK61phPaYCeEge_1753752512","step":"group_by_applicant"} 
[2025-07-29 09:28:34] local.INFO: ✅ 考生分組完成 {"task_id":"merge_95rK61phPaYCeEge_1753752512","applicant_count":1,"step":"group_by_applicant_success"} 
[2025-07-29 09:28:34] local.INFO: 📁 步驟3: 準備檔案列表 {"task_id":"merge_95rK61phPaYCeEge_1753752512","step":"prepare_files","applicant_count":1} 
[2025-07-29 09:28:34] local.DEBUG: 🔄 處理考生檔案 {"task_id":"merge_95rK61phPaYCeEge_1753752512","applicant_id":1,"recommendation_count":2,"progress":1,"total":1} 
[2025-07-29 09:28:34] local.DEBUG: ✅ 考生檔案準備完成 {"task_id":"merge_95rK61phPaYCeEge_1753752512","applicant_id":1,"file_count":2} 
[2025-07-29 09:28:34] local.INFO: 📊 檔案準備統計 {"task_id":"merge_95rK61phPaYCeEge_1753752512","total_files":2,"processed_applicants":1,"step":"prepare_files_complete"} 
[2025-07-29 09:28:34] local.INFO: 🗜️ 步驟4: 開始建立ZIP檔案 {"task_id":"merge_95rK61phPaYCeEge_1753752512","step":"create_zip","file_count":2,"memory_usage":31457280} 
[2025-07-29 09:28:34] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1.pdf","zip_path":"348/1.pdf","file_size":47140} 
[2025-07-29 09:28:34] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/349/2.pdf","zip_path":"348/2.pdf","file_size":488789} 
[2025-07-29 09:28:34] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-29 09:28:34] local.INFO: ZIP檔案創建成功 {"task_id":"merge_95rK61phPaYCeEge_1753752512","zip_path":"pdf_merges/recommendations_2_114_2025-07-29_09-28-34.zip","full_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/public\\pdf_merges/recommendations_2_114_2025-07-29_09-28-34.zip","file_count":2,"file_size":499711} 
[2025-07-29 09:28:34] local.INFO: ✅ ZIP檔案建立完成 {"task_id":"merge_95rK61phPaYCeEge_1753752512","zip_file_path":"pdf_merges/recommendations_2_114_2025-07-29_09-28-34.zip","step":"create_zip_success"} 
[2025-07-29 09:28:34] local.INFO: 🔗 步驟5: 生成下載URL {"task_id":"merge_95rK61phPaYCeEge_1753752512","step":"generate_download_url"} 
[2025-07-29 09:28:34] local.INFO: ✅ 下載URL生成完成 {"task_id":"merge_95rK61phPaYCeEge_1753752512","download_url":"http://rec-letter.test/api/public/pdf-download/merge_95rK61phPaYCeEge_1753752512","step":"generate_download_url_success"} 
[2025-07-29 09:28:34] local.INFO: 🏁 步驟6: 標記任務完成 {"task_id":"merge_95rK61phPaYCeEge_1753752512","step":"mark_as_ready"} 
[2025-07-29 09:28:34] local.INFO: 🎉 PDF壓縮任務完成 {"task_id":"merge_95rK61phPaYCeEge_1753752512","job_id":14,"compressed_files":2,"zip_file":"pdf_merges/recommendations_2_114_2025-07-29_09-28-34.zip","download_url":"http://rec-letter.test/api/public/pdf-download/merge_95rK61phPaYCeEge_1753752512","total_execution_time":0.49468183517456055,"memory_peak":31457280,"step":"job_complete"} 
[2025-07-29 09:28:37] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-29 09:28:37] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_95rK61phPaYCeEge_1753752512"},"input":{"task_id":"merge_95rK61phPaYCeEge_1753752512"},"ip":"127.0.0.1"} 
[2025-07-29 09:28:37] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_95rK61phPaYCeEge_1753752512","status":"ready","message":"任務完成，可以下載","ip":"127.0.0.1"} 
[2025-07-29 09:28:38] local.INFO: 公共PDF壓縮檔案下載 {"task_id":"merge_95rK61phPaYCeEge_1753752512","file_path":"pdf_merges/recommendations_2_114_2025-07-29_09-28-34.zip","client_ip":"127.0.0.1","user_agent":"GuzzleHttp/7"} 
[2025-07-29 09:53:19] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-29 09:53:20] local.INFO: 🚀 準備派發PDF壓縮Job到Queue {"task_id":"merge_266klvIDF2dAMNQ2_1753754000","queue_connection":"sync","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-29T01:53:19.992234Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-29 09:53:20] local.INFO: ✅ PDF壓縮Job已成功派發到Queue {"task_id":"merge_266klvIDF2dAMNQ2_1753754000","job_id":"unknown","queue_connection":"sync","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-29T01:53:19.992234Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-29 09:53:20] local.INFO: 📋 PDF壓縮任務已啟動 {"task_id":"merge_266klvIDF2dAMNQ2_1753754000","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-29T01:53:19.992234Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-29 09:53:20] local.INFO: 🚀 PDF壓縮Job開始執行 {"task_id":"merge_266klvIDF2dAMNQ2_1753754000","job_id":"","queue":"sync","attempts":1,"max_tries":3,"timeout":1800,"parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-29T01:53:19.992234Z","client_ip":"127.0.0.1"},"memory_usage":4194304,"memory_peak":4194304,"timestamp":"2025-07-29T01:53:20.224758Z"} 
[2025-07-29 09:53:20] local.INFO: 📋 開始處理PDF壓縮任務 {"task_id":"merge_266klvIDF2dAMNQ2_1753754000","job_id":"","task_status":"processing","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-29T01:53:19.992234Z","client_ip":"127.0.0.1"},"step":"initialization"} 
[2025-07-29 09:53:20] local.INFO: 📂 步驟1: 獲取推薦函資料 {"task_id":"merge_266klvIDF2dAMNQ2_1753754000","step":"get_recommendations","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-29T01:53:19.992234Z","client_ip":"127.0.0.1"}} 
[2025-07-29 09:53:20] local.INFO: ✅ 成功獲取推薦函 {"task_id":"merge_266klvIDF2dAMNQ2_1753754000","recommendation_count":2,"step":"get_recommendations_success"} 
[2025-07-29 09:53:20] local.INFO: 👥 步驟2: 按考生分組 {"task_id":"merge_266klvIDF2dAMNQ2_1753754000","step":"group_by_applicant"} 
[2025-07-29 09:53:20] local.INFO: ✅ 考生分組完成 {"task_id":"merge_266klvIDF2dAMNQ2_1753754000","applicant_count":1,"step":"group_by_applicant_success"} 
[2025-07-29 09:53:20] local.INFO: 📁 步驟3: 準備檔案列表 {"task_id":"merge_266klvIDF2dAMNQ2_1753754000","step":"prepare_files","applicant_count":1} 
[2025-07-29 09:53:20] local.DEBUG: 🔄 處理考生檔案 {"task_id":"merge_266klvIDF2dAMNQ2_1753754000","applicant_id":1,"recommendation_count":2,"progress":1,"total":1} 
[2025-07-29 09:53:20] local.DEBUG: ✅ 考生檔案準備完成 {"task_id":"merge_266klvIDF2dAMNQ2_1753754000","applicant_id":1,"file_count":2} 
[2025-07-29 09:53:20] local.INFO: 📊 檔案準備統計 {"task_id":"merge_266klvIDF2dAMNQ2_1753754000","total_files":2,"processed_applicants":1,"step":"prepare_files_complete"} 
[2025-07-29 09:53:20] local.INFO: 🗜️ 步驟4: 開始建立ZIP檔案 {"task_id":"merge_266klvIDF2dAMNQ2_1753754000","step":"create_zip","file_count":2,"memory_usage":4194304} 
[2025-07-29 09:53:20] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1.pdf","zip_path":"348/1.pdf","file_size":47140} 
[2025-07-29 09:53:20] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/349/2.pdf","zip_path":"348/2.pdf","file_size":488789} 
[2025-07-29 09:53:20] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-29 09:53:20] local.INFO: ZIP檔案創建成功 {"task_id":"merge_266klvIDF2dAMNQ2_1753754000","zip_path":"pdf_merges/recommendations_2_114_2025-07-29_09-53-20.zip","full_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/public\\pdf_merges/recommendations_2_114_2025-07-29_09-53-20.zip","file_count":2,"file_size":499711} 
[2025-07-29 09:53:20] local.INFO: ✅ ZIP檔案建立完成 {"task_id":"merge_266klvIDF2dAMNQ2_1753754000","zip_file_path":"pdf_merges/recommendations_2_114_2025-07-29_09-53-20.zip","step":"create_zip_success"} 
[2025-07-29 09:53:20] local.INFO: 🔗 步驟5: 生成下載URL {"task_id":"merge_266klvIDF2dAMNQ2_1753754000","step":"generate_download_url"} 
[2025-07-29 09:53:20] local.INFO: ✅ 下載URL生成完成 {"task_id":"merge_266klvIDF2dAMNQ2_1753754000","download_url":"https://rec-letter.test/api/public/pdf-download/merge_266klvIDF2dAMNQ2_1753754000","step":"generate_download_url_success"} 
[2025-07-29 09:53:20] local.INFO: 🏁 步驟6: 標記任務完成 {"task_id":"merge_266klvIDF2dAMNQ2_1753754000","step":"mark_as_ready"} 
[2025-07-29 09:53:20] local.INFO: 🎉 PDF壓縮任務完成 {"task_id":"merge_266klvIDF2dAMNQ2_1753754000","job_id":"","compressed_files":2,"zip_file":"pdf_merges/recommendations_2_114_2025-07-29_09-53-20.zip","download_url":"https://rec-letter.test/api/public/pdf-download/merge_266klvIDF2dAMNQ2_1753754000","total_execution_time":0.602686882019043,"memory_peak":4194304,"step":"job_complete"} 
[2025-07-29 09:53:25] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-29 09:53:25] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_266klvIDF2dAMNQ2_1753754000"},"input":{"task_id":"merge_266klvIDF2dAMNQ2_1753754000"},"ip":"127.0.0.1"} 
[2025-07-29 09:53:25] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_266klvIDF2dAMNQ2_1753754000","status":"ready","message":"任務完成，可以下載","ip":"127.0.0.1"} 
[2025-07-29 09:53:26] local.INFO: 公共PDF壓縮檔案下載 {"task_id":"merge_266klvIDF2dAMNQ2_1753754000","file_path":"pdf_merges/recommendations_2_114_2025-07-29_09-53-20.zip","client_ip":"127.0.0.1","user_agent":"GuzzleHttp/7"} 
[2025-07-29 10:11:19] local.INFO: 開始同步招生期間資料 {"api_url":"http://localhost:18001/index.php/api/v1/recommendation_system/sync_exam_period"} 
[2025-07-29 10:11:30] local.INFO: 招生期間資料已保存 {"periods_count":10,"data":[{"exam_id":"2","exam_name":"碩士班甄試招生(測試)","app_date1_start":"2025/07/01 09:00:00","app_date1_end":"2025/08/30 17:00:00","synced_at":"2025-07-29T02:11:29.948030Z"},{"exam_id":"K","exam_name":"技優甄審指定項目甄審報名(測試)","app_date1_start":"2025/06/05 10:00:00","app_date1_end":"2025/06/10 17:00:00","synced_at":"2025-07-29T02:11:29.948825Z"},{"exam_id":"E","exam_name":"單獨招收身心障礙學生考試(測試)","app_date1_start":"2025/04/28 09:00:00","app_date1_end":"2025/05/09 17:00:00","synced_at":"2025-07-29T02:11:29.949023Z"},{"exam_id":"J","exam_name":"新住民入學招生(測試)","app_date1_start":"2025/04/21 09:00:00","app_date1_end":"2025/05/02 17:00:00","synced_at":"2025-07-29T02:11:29.949173Z"},{"exam_id":"7","exam_name":"大學申請入學指定項目甄試(測試)","app_date1_start":"2025/04/08 10:00:00","app_date1_end":"2025/05/01 17:00:00","synced_at":"2025-07-29T02:11:29.949326Z"},{"exam_id":"1","exam_name":"博士班考試招生(測試)","app_date1_start":"2025/03/18 09:00:00","app_date1_end":"2025/04/08 17:00:00","synced_at":"2025-07-29T02:11:29.949469Z"},{"exam_id":"P","exam_name":"碩士在職專班招生(測試)","app_date1_start":"2025/02/18 09:00:00","app_date1_end":"2025/03/25 17:00:00","synced_at":"2025-07-29T02:11:29.949615Z"},{"exam_id":"3","exam_name":"碩士班考試招生(測試)","app_date1_start":"2025/01/03 09:00:00","app_date1_end":"2025/01/22 17:00:00","synced_at":"2025-07-29T02:11:29.949761Z"},{"exam_id":"B","exam_name":"原住民專班招生(測試)","app_date1_start":"2024/12/03 09:00:00","app_date1_end":"2025/02/27 17:00:00","synced_at":"2025-07-29T02:11:29.949899Z"},{"exam_id":"I","exam_name":"特殊選才單獨招生(測試)","app_date1_start":"2024/10/25 09:00:00","app_date1_end":"2024/11/11 17:00:00","synced_at":"2025-07-29T02:11:29.950026Z"}]} 
[2025-07-29 10:11:30] local.INFO: 系統初始化時成功同步外部資料 {"success":true,"message":"系統設定同步完成","results":{"exam_periods":{"success":true,"message":"招生期間資料同步成功","data":{"updated_count":10,"processed_periods":[{"exam_id":"2","exam_name":"碩士班甄試招生(測試)","app_date1_start":"2025/07/01 09:00:00","app_date1_end":"2025/08/30 17:00:00","synced_at":"2025-07-29T02:11:29.948030Z"},{"exam_id":"K","exam_name":"技優甄審指定項目甄審報名(測試)","app_date1_start":"2025/06/05 10:00:00","app_date1_end":"2025/06/10 17:00:00","synced_at":"2025-07-29T02:11:29.948825Z"},{"exam_id":"E","exam_name":"單獨招收身心障礙學生考試(測試)","app_date1_start":"2025/04/28 09:00:00","app_date1_end":"2025/05/09 17:00:00","synced_at":"2025-07-29T02:11:29.949023Z"},{"exam_id":"J","exam_name":"新住民入學招生(測試)","app_date1_start":"2025/04/21 09:00:00","app_date1_end":"2025/05/02 17:00:00","synced_at":"2025-07-29T02:11:29.949173Z"},{"exam_id":"7","exam_name":"大學申請入學指定項目甄試(測試)","app_date1_start":"2025/04/08 10:00:00","app_date1_end":"2025/05/01 17:00:00","synced_at":"2025-07-29T02:11:29.949326Z"},{"exam_id":"1","exam_name":"博士班考試招生(測試)","app_date1_start":"2025/03/18 09:00:00","app_date1_end":"2025/04/08 17:00:00","synced_at":"2025-07-29T02:11:29.949469Z"},{"exam_id":"P","exam_name":"碩士在職專班招生(測試)","app_date1_start":"2025/02/18 09:00:00","app_date1_end":"2025/03/25 17:00:00","synced_at":"2025-07-29T02:11:29.949615Z"},{"exam_id":"3","exam_name":"碩士班考試招生(測試)","app_date1_start":"2025/01/03 09:00:00","app_date1_end":"2025/01/22 17:00:00","synced_at":"2025-07-29T02:11:29.949761Z"},{"exam_id":"B","exam_name":"原住民專班招生(測試)","app_date1_start":"2024/12/03 09:00:00","app_date1_end":"2025/02/27 17:00:00","synced_at":"2025-07-29T02:11:29.949899Z"},{"exam_id":"I","exam_name":"特殊選才單獨招生(測試)","app_date1_start":"2024/10/25 09:00:00","app_date1_end":"2024/11/11 17:00:00","synced_at":"2025-07-29T02:11:29.950026Z"}]}}}} 
[2025-07-29 13:24:28] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/auth-from-external","method":"GET"} 
[2025-07-29 13:24:35] local.INFO: 建立新使用者帳號 {"user_id":2,"email":"<EMAIL>","role":"applicant"} 
[2025-07-29 13:24:35] local.INFO: 建立新考生記錄 {"user_id":2,"applicant_id":1,"external_uid":"ZmOnRv1mNU6+zsms5A1fCg==","exam_year":"114","exam_id":"2"} 
[2025-07-29 13:24:36] local.INFO: 考生eapapi登入成功 {"external_uid":"ZmOnRv1mNU6+zsms5A1fCg==","exam_year":114,"exam_id":"2","user_id":2,"applicant_id":1,"ip":"127.0.0.1"} 
[2025-07-29 13:24:47] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-29T05:24:47.758921Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-29 13:24:47] local.INFO: 【Middleware】考生存取檢查中間件觸發 {"user":2} 
[2025-07-29 13:24:47] local.INFO: 考生嘗試建立推薦函: {"user_id":2,"applicant_id":1,"request_data":{"recommender_email":"<EMAIL>","recommender_name":"吳宗霖","recommender_title":null,"recommender_phone":null,"recommender_department":null,"department_name":"機械工程學系碩士班","program_type":"碩士班甄試招生(測試)","external_autono":348}} 
[2025-07-29 13:24:47] local.INFO: 建立新的推薦人使用者帳號 {"user_id":3,"email":"<EMAIL>","name":"吳宗霖"} 
[2025-07-29 13:24:47] local.INFO: 嘗試新增推薦人資料 {"user_id":3,"email":"<EMAIL>","name":"吳宗霖","title":"","department":"","phone":"","login_token":"尚未生成","exam_year":114,"exam_id":"2"} 
[2025-07-29 13:24:47] local.DEBUG: Generated secure login token {"token_length":64,"timestamp":1753766687} 
[2025-07-29 13:24:47] local.INFO: 建立新的推薦人資料 {"recommender_id":1,"user_id":3,"email":"<EMAIL>","exam_year":114,"exam_id":"2"} 
[2025-07-29 13:24:48] local.INFO: 外部 API 郵件發送成功 {"recipient":"<EMAIL>","subject":"推薦函邀請 – 碩士班甄試招生(測試)（機械工程學系碩士班）","api_response":"{\"status\":\"success\",\"detail\":\"Message has been sent\"}"} 
[2025-07-29 13:24:48] local.INFO: 推薦函邀請信發送成功 {"recommendation_id":1,"recommender_email":"<EMAIL>","email_log_id":1} 
[2025-07-29 13:24:54] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-29T05:24:54.643704Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-29 13:24:54] local.INFO: 【Middleware】考生存取檢查中間件觸發 {"user":2} 
[2025-07-29 13:24:54] local.INFO: 考生嘗試建立推薦函: {"user_id":2,"applicant_id":1,"request_data":{"recommender_email":"<EMAIL>","recommender_name":"吳宗霖","recommender_title":null,"recommender_phone":null,"recommender_department":null,"department_name":"資訊工程學系碩士班","program_type":"碩士班甄試招生(測試)","external_autono":349}} 
[2025-07-29 13:24:54] local.INFO: 更新推薦人考試資訊 {"recommender_id":1,"updated_fields":["exam_year","exam_id"]} 
[2025-07-29 13:24:55] local.INFO: 外部 API 郵件發送成功 {"recipient":"<EMAIL>","subject":"推薦函邀請 – 碩士班甄試招生(測試)（資訊工程學系碩士班）","api_response":"{\"status\":\"success\",\"detail\":\"Message has been sent\"}"} 
[2025-07-29 13:24:55] local.INFO: 推薦函邀請信發送成功 {"recommendation_id":2,"recommender_email":"<EMAIL>","email_log_id":2} 
[2025-07-29 13:25:00] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-29T05:25:00.310123Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-29 13:25:00] local.INFO: 【Middleware】考生存取檢查中間件觸發 {"user":2} 
[2025-07-29 13:25:00] local.INFO: 考生嘗試建立推薦函: {"user_id":2,"applicant_id":1,"request_data":{"recommender_email":"<EMAIL>","recommender_name":"Mike","recommender_title":null,"recommender_phone":null,"recommender_department":null,"department_name":"機械工程學系碩士班","program_type":"碩士班甄試招生(測試)","external_autono":348}} 
[2025-07-29 13:25:00] local.INFO: 建立新的推薦人使用者帳號 {"user_id":4,"email":"<EMAIL>","name":"Mike"} 
[2025-07-29 13:25:00] local.INFO: 嘗試新增推薦人資料 {"user_id":4,"email":"<EMAIL>","name":"Mike","title":"","department":"","phone":"","login_token":"尚未生成","exam_year":114,"exam_id":"2"} 
[2025-07-29 13:25:00] local.DEBUG: Generated secure login token {"token_length":64,"timestamp":1753766700} 
[2025-07-29 13:25:00] local.INFO: 建立新的推薦人資料 {"recommender_id":2,"user_id":4,"email":"<EMAIL>","exam_year":114,"exam_id":"2"} 
[2025-07-29 13:25:00] local.INFO: 外部 API 郵件發送成功 {"recipient":"<EMAIL>","subject":"推薦函邀請 – 碩士班甄試招生(測試)（機械工程學系碩士班）","api_response":"{\"status\":\"success\",\"detail\":\"Message has been sent\"}"} 
[2025-07-29 13:25:00] local.INFO: 推薦函邀請信發送成功 {"recommendation_id":3,"recommender_email":"<EMAIL>","email_log_id":3} 
[2025-07-29 13:25:07] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-29T05:25:07.123505Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-29 13:25:07] local.INFO: 【Middleware】考生存取檢查中間件觸發 {"user":2} 
[2025-07-29 13:25:07] local.INFO: 考生嘗試建立推薦函: {"user_id":2,"applicant_id":1,"request_data":{"recommender_email":"<EMAIL>","recommender_name":"Mike","recommender_title":null,"recommender_phone":null,"recommender_department":null,"department_name":"資訊工程學系碩士班","program_type":"碩士班甄試招生(測試)","external_autono":349}} 
[2025-07-29 13:25:07] local.INFO: 更新推薦人考試資訊 {"recommender_id":2,"updated_fields":["exam_year","exam_id"]} 
[2025-07-29 13:25:07] local.INFO: 外部 API 郵件發送成功 {"recipient":"<EMAIL>","subject":"推薦函邀請 – 碩士班甄試招生(測試)（資訊工程學系碩士班）","api_response":"{\"status\":\"success\",\"detail\":\"Message has been sent\"}"} 
[2025-07-29 13:25:07] local.INFO: 推薦函邀請信發送成功 {"recommendation_id":4,"recommender_email":"<EMAIL>","email_log_id":4} 
[2025-07-29 13:25:24] local.INFO: 使用者登出 {"user_type":"admin","ip":"127.0.0.1"} 
[2025-07-29 13:25:51] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/auth-from-external","method":"GET"} 
[2025-07-29 13:25:57] local.INFO: 建立新使用者帳號 {"user_id":5,"email":"<EMAIL>","role":"applicant"} 
[2025-07-29 13:25:57] local.INFO: 建立新考生記錄 {"user_id":5,"applicant_id":2,"external_uid":"K8Hfdru39mAcQUoGmGl0zA==","exam_year":"114","exam_id":"2"} 
[2025-07-29 13:25:57] local.INFO: 考生eapapi登入成功 {"external_uid":"K8Hfdru39mAcQUoGmGl0zA==","exam_year":114,"exam_id":"2","user_id":5,"applicant_id":2,"ip":"127.0.0.1"} 
[2025-07-29 13:26:06] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-29T05:26:06.072983Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-29 13:26:06] local.INFO: 【Middleware】考生存取檢查中間件觸發 {"user":5} 
[2025-07-29 13:26:06] local.INFO: 考生嘗試建立推薦函: {"user_id":5,"applicant_id":2,"request_data":{"recommender_email":"<EMAIL>","recommender_name":"吳宗霖","recommender_title":null,"recommender_phone":null,"recommender_department":null,"department_name":"臺灣語文與傳播學系碩士班","program_type":"碩士班甄試招生(測試)","external_autono":351}} 
[2025-07-29 13:26:06] local.INFO: 更新推薦人考試資訊 {"recommender_id":1,"updated_fields":["exam_year","exam_id"]} 
[2025-07-29 13:26:06] local.INFO: 外部 API 郵件發送成功 {"recipient":"<EMAIL>","subject":"推薦函邀請 – 碩士班甄試招生(測試)（臺灣語文與傳播學系碩士班）","api_response":"{\"status\":\"success\",\"detail\":\"Message has been sent\"}"} 
[2025-07-29 13:26:06] local.INFO: 推薦函邀請信發送成功 {"recommendation_id":5,"recommender_email":"<EMAIL>","email_log_id":5} 
[2025-07-29 13:26:11] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-29T05:26:11.455686Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-29 13:26:11] local.INFO: 【Middleware】考生存取檢查中間件觸發 {"user":5} 
[2025-07-29 13:26:11] local.INFO: 考生嘗試建立推薦函: {"user_id":5,"applicant_id":2,"request_data":{"recommender_email":"<EMAIL>","recommender_name":"Mike","recommender_title":null,"recommender_phone":null,"recommender_department":null,"department_name":"臺灣語文與傳播學系碩士班","program_type":"碩士班甄試招生(測試)","external_autono":351}} 
[2025-07-29 13:26:11] local.INFO: 更新推薦人考試資訊 {"recommender_id":2,"updated_fields":["exam_year","exam_id"]} 
[2025-07-29 13:26:11] local.INFO: 外部 API 郵件發送成功 {"recipient":"<EMAIL>","subject":"推薦函邀請 – 碩士班甄試招生(測試)（臺灣語文與傳播學系碩士班）","api_response":"{\"status\":\"success\",\"detail\":\"Message has been sent\"}"} 
[2025-07-29 13:26:11] local.INFO: 推薦函邀請信發送成功 {"recommendation_id":6,"recommender_email":"<EMAIL>","email_log_id":6} 
[2025-07-29 13:26:17] local.INFO: 使用者登出 {"user_type":"admin","ip":"127.0.0.1"} 
[2025-07-29 13:26:27] local.INFO: 推薦人登入成功 {"recommender_id":2,"user_id":4,"token":"7c1bdff97ed23b00a3e22a9c48269153d7a6b81c2b7b295460bd799d5180bfd4","ip":"127.0.0.1"} 
[2025-07-29 13:37:46] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-29T05:37:46.343204Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-29 13:37:46] local.INFO: 【Middleware】推薦人存取檢查中間件觸發 {"user":4} 
[2025-07-29 13:37:46] local.INFO: PDF檔案已存儲 {"recommendation_id":3,"file_path":"recommendations/2/114/348/2.pdf","file_size":277531,"original_name":"Mike.pdf","is_overwrite":false,"recommender_id":2} 
[2025-07-29 13:37:46] local.INFO: 推薦函 PDF 上傳成功 {"recommendation_id":"3","file_path":"recommendations/2/114/348/2.pdf","file_size":277531} 
[2025-07-29 13:37:46] local.INFO: 外部 API 郵件發送成功 {"recipient":"<EMAIL>","subject":"推薦函已提交通知 – 碩士班甄試招生(測試)（機械工程學系碩士班）","api_response":"{\"status\":\"success\",\"detail\":\"Message has been sent\"}"} 
[2025-07-29 13:37:46] local.INFO: 推薦函提交通知信發送成功 {"recommendation_id":3,"applicant_email":"<EMAIL>","email_log_id":7} 
[2025-07-29 13:37:46] local.INFO: 推薦函提交成功 {"recommendation_id":"3","submission_type":"pdf","user_id":4} 
[2025-07-29 13:37:54] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-29T05:37:54.438646Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-29 13:37:54] local.INFO: 【Middleware】推薦人存取檢查中間件觸發 {"user":4} 
[2025-07-29 13:37:54] local.INFO: PDF檔案已存儲 {"recommendation_id":4,"file_path":"recommendations/2/114/349/2.pdf","file_size":277531,"original_name":"Mike.pdf","is_overwrite":false,"recommender_id":2} 
[2025-07-29 13:37:54] local.INFO: 推薦函 PDF 上傳成功 {"recommendation_id":"4","file_path":"recommendations/2/114/349/2.pdf","file_size":277531} 
[2025-07-29 13:37:54] local.INFO: 外部 API 郵件發送成功 {"recipient":"<EMAIL>","subject":"推薦函已提交通知 – 碩士班甄試招生(測試)（資訊工程學系碩士班）","api_response":"{\"status\":\"success\",\"detail\":\"Message has been sent\"}"} 
[2025-07-29 13:37:54] local.INFO: 推薦函提交通知信發送成功 {"recommendation_id":4,"applicant_email":"<EMAIL>","email_log_id":8} 
[2025-07-29 13:37:54] local.INFO: 推薦函提交成功 {"recommendation_id":"4","submission_type":"pdf","user_id":4} 
[2025-07-29 13:38:03] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-29T05:38:03.252758Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-29 13:38:03] local.INFO: 【Middleware】推薦人存取檢查中間件觸發 {"user":4} 
[2025-07-29 13:38:03] local.INFO: PDF檔案已存儲 {"recommendation_id":6,"file_path":"recommendations/2/114/351/2.pdf","file_size":277531,"original_name":"Mike.pdf","is_overwrite":false,"recommender_id":2} 
[2025-07-29 13:38:03] local.INFO: 推薦函 PDF 上傳成功 {"recommendation_id":"6","file_path":"recommendations/2/114/351/2.pdf","file_size":277531} 
[2025-07-29 13:38:03] local.INFO: 外部 API 郵件發送成功 {"recipient":"<EMAIL>","subject":"推薦函已提交通知 – 碩士班甄試招生(測試)（臺灣語文與傳播學系碩士班）","api_response":"{\"status\":\"success\",\"detail\":\"Message has been sent\"}"} 
[2025-07-29 13:38:03] local.INFO: 推薦函提交通知信發送成功 {"recommendation_id":6,"applicant_email":"<EMAIL>","email_log_id":9} 
[2025-07-29 13:38:03] local.INFO: 推薦函提交成功 {"recommendation_id":"6","submission_type":"pdf","user_id":4} 
[2025-07-29 13:38:07] local.INFO: 使用者登出 {"user_type":"admin","ip":"127.0.0.1"} 
[2025-07-29 13:38:19] local.INFO: 推薦人登入成功 {"recommender_id":1,"user_id":3,"token":"cf4248153ffcb02e889adb7e6ccc419fedffc2414b7c6aa87deb30c358e1c721","ip":"127.0.0.1"} 
[2025-07-29 13:38:39] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-29T05:38:39.534853Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-29 13:38:39] local.INFO: 【Middleware】推薦人存取檢查中間件觸發 {"user":3} 
[2025-07-29 13:38:39] local.INFO: PDF檔案已存儲 {"recommendation_id":1,"file_path":"recommendations/2/114/348/1.pdf","file_size":30293,"original_name":"陳.pdf","is_overwrite":false,"recommender_id":1} 
[2025-07-29 13:38:39] local.INFO: 推薦函 PDF 上傳成功 {"recommendation_id":"1","file_path":"recommendations/2/114/348/1.pdf","file_size":30293} 
[2025-07-29 13:38:39] local.INFO: 外部 API 郵件發送成功 {"recipient":"<EMAIL>","subject":"推薦函已提交通知 – 碩士班甄試招生(測試)（機械工程學系碩士班）","api_response":"{\"status\":\"success\",\"detail\":\"Message has been sent\"}"} 
[2025-07-29 13:38:39] local.INFO: 推薦函提交通知信發送成功 {"recommendation_id":1,"applicant_email":"<EMAIL>","email_log_id":10} 
[2025-07-29 13:38:39] local.INFO: 推薦函提交成功 {"recommendation_id":"1","submission_type":"pdf","user_id":3} 
[2025-07-29 13:38:47] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-29T05:38:47.100185Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-29 13:38:47] local.INFO: 【Middleware】推薦人存取檢查中間件觸發 {"user":3} 
[2025-07-29 13:38:47] local.INFO: PDF檔案已存儲 {"recommendation_id":2,"file_path":"recommendations/2/114/349/1.pdf","file_size":30293,"original_name":"陳.pdf","is_overwrite":false,"recommender_id":1} 
[2025-07-29 13:38:47] local.INFO: 推薦函 PDF 上傳成功 {"recommendation_id":"2","file_path":"recommendations/2/114/349/1.pdf","file_size":30293} 
[2025-07-29 13:38:47] local.INFO: 外部 API 郵件發送成功 {"recipient":"<EMAIL>","subject":"推薦函已提交通知 – 碩士班甄試招生(測試)（資訊工程學系碩士班）","api_response":"{\"status\":\"success\",\"detail\":\"Message has been sent\"}"} 
[2025-07-29 13:38:47] local.INFO: 推薦函提交通知信發送成功 {"recommendation_id":2,"applicant_email":"<EMAIL>","email_log_id":11} 
[2025-07-29 13:38:47] local.INFO: 推薦函提交成功 {"recommendation_id":"2","submission_type":"pdf","user_id":3} 
[2025-07-29 13:38:55] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-29T05:38:55.418660Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-29 13:38:55] local.INFO: 【Middleware】推薦人存取檢查中間件觸發 {"user":3} 
[2025-07-29 13:38:55] local.INFO: PDF檔案已存儲 {"recommendation_id":5,"file_path":"recommendations/2/114/351/1.pdf","file_size":30293,"original_name":"陳.pdf","is_overwrite":false,"recommender_id":1} 
[2025-07-29 13:38:55] local.INFO: 推薦函 PDF 上傳成功 {"recommendation_id":"5","file_path":"recommendations/2/114/351/1.pdf","file_size":30293} 
[2025-07-29 13:38:55] local.INFO: 外部 API 郵件發送成功 {"recipient":"<EMAIL>","subject":"推薦函已提交通知 – 碩士班甄試招生(測試)（臺灣語文與傳播學系碩士班）","api_response":"{\"status\":\"success\",\"detail\":\"Message has been sent\"}"} 
[2025-07-29 13:38:55] local.INFO: 推薦函提交通知信發送成功 {"recommendation_id":5,"applicant_email":"<EMAIL>","email_log_id":12} 
[2025-07-29 13:38:55] local.INFO: 推薦函提交成功 {"recommendation_id":"5","submission_type":"pdf","user_id":3} 
[2025-07-29 13:53:15] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-29 13:53:15] local.INFO: PDF壓縮任務執行 {"task_id":"merge_DQG0rcTM8LX3LDcS_1753768395","queue":"sync"} 
[2025-07-29 13:53:15] local.INFO: 🚀 PDF壓縮Job開始執行 {"task_id":"merge_DQG0rcTM8LX3LDcS_1753768395","job_id":"","queue":"sync","attempts":1,"max_tries":3,"timeout":1800,"parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-29T05:53:15.297330Z","client_ip":"127.0.0.1"},"memory_usage":4194304,"memory_peak":4194304,"timestamp":"2025-07-29T05:53:15.365593Z"} 
[2025-07-29 13:53:15] local.INFO: 📋 開始處理PDF壓縮任務 {"task_id":"merge_DQG0rcTM8LX3LDcS_1753768395","job_id":"","task_status":"processing","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-29T05:53:15.297330Z","client_ip":"127.0.0.1"},"step":"initialization"} 
[2025-07-29 13:53:15] local.INFO: 📂 步驟1: 獲取推薦函資料 {"task_id":"merge_DQG0rcTM8LX3LDcS_1753768395","step":"get_recommendations","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-29T05:53:15.297330Z","client_ip":"127.0.0.1"}} 
[2025-07-29 13:53:15] local.INFO: ✅ 成功獲取推薦函 {"task_id":"merge_DQG0rcTM8LX3LDcS_1753768395","recommendation_count":6,"step":"get_recommendations_success"} 
[2025-07-29 13:53:15] local.INFO: 👥 步驟2: 按考生分組 {"task_id":"merge_DQG0rcTM8LX3LDcS_1753768395","step":"group_by_applicant"} 
[2025-07-29 13:53:15] local.INFO: ✅ 考生分組完成 {"task_id":"merge_DQG0rcTM8LX3LDcS_1753768395","applicant_count":2,"step":"group_by_applicant_success"} 
[2025-07-29 13:53:15] local.INFO: 📁 步驟3: 準備檔案列表 {"task_id":"merge_DQG0rcTM8LX3LDcS_1753768395","step":"prepare_files","applicant_count":2} 
[2025-07-29 13:53:15] local.DEBUG: 🔄 處理考生檔案 {"task_id":"merge_DQG0rcTM8LX3LDcS_1753768395","applicant_id":1,"recommendation_count":4,"progress":1,"total":2} 
[2025-07-29 13:53:15] local.DEBUG: ✅ 考生檔案準備完成 {"task_id":"merge_DQG0rcTM8LX3LDcS_1753768395","applicant_id":1,"file_count":4} 
[2025-07-29 13:53:15] local.DEBUG: 🔄 處理考生檔案 {"task_id":"merge_DQG0rcTM8LX3LDcS_1753768395","applicant_id":2,"recommendation_count":2,"progress":2,"total":2} 
[2025-07-29 13:53:15] local.DEBUG: ✅ 考生檔案準備完成 {"task_id":"merge_DQG0rcTM8LX3LDcS_1753768395","applicant_id":2,"file_count":2} 
[2025-07-29 13:53:15] local.INFO: 📊 檔案準備統計 {"task_id":"merge_DQG0rcTM8LX3LDcS_1753768395","total_files":6,"processed_applicants":2,"step":"prepare_files_complete"} 
[2025-07-29 13:53:15] local.INFO: 🗜️ 步驟4: 開始建立ZIP檔案 {"task_id":"merge_DQG0rcTM8LX3LDcS_1753768395","step":"create_zip","file_count":6,"memory_usage":4194304} 
[2025-07-29 13:53:15] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1.pdf","zip_path":"348/1.pdf","file_size":30293} 
[2025-07-29 13:53:15] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/349/1.pdf","zip_path":"348/2.pdf","file_size":30293} 
[2025-07-29 13:53:15] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/2.pdf","zip_path":"348/3.pdf","file_size":277531} 
[2025-07-29 13:53:15] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/349/2.pdf","zip_path":"348/4.pdf","file_size":277531} 
[2025-07-29 13:53:15] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/351/1.pdf","zip_path":"351/1.pdf","file_size":30293} 
[2025-07-29 13:53:15] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/351/2.pdf","zip_path":"351/2.pdf","file_size":277531} 
[2025-07-29 13:53:15] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-29 13:53:15] local.INFO: ZIP檔案創建成功 {"task_id":"merge_DQG0rcTM8LX3LDcS_1753768395","zip_path":"pdf_merges/recommendations_2_114_2025-07-29_13-53-15.zip","full_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/public\\pdf_merges/recommendations_2_114_2025-07-29_13-53-15.zip","file_count":6,"file_size":883520} 
[2025-07-29 13:53:15] local.INFO: ✅ ZIP檔案建立完成 {"task_id":"merge_DQG0rcTM8LX3LDcS_1753768395","zip_file_path":"pdf_merges/recommendations_2_114_2025-07-29_13-53-15.zip","step":"create_zip_success"} 
[2025-07-29 13:53:15] local.INFO: 🔗 步驟5: 生成下載URL {"task_id":"merge_DQG0rcTM8LX3LDcS_1753768395","step":"generate_download_url"} 
[2025-07-29 13:53:15] local.INFO: ✅ 下載URL生成完成 {"task_id":"merge_DQG0rcTM8LX3LDcS_1753768395","download_url":"https://rec-letter.test/api/public/pdf-download/merge_DQG0rcTM8LX3LDcS_1753768395","step":"generate_download_url_success"} 
[2025-07-29 13:53:15] local.INFO: 🏁 步驟6: 標記任務完成 {"task_id":"merge_DQG0rcTM8LX3LDcS_1753768395","step":"mark_as_ready"} 
[2025-07-29 13:53:15] local.INFO: 🎉 PDF壓縮任務完成 {"task_id":"merge_DQG0rcTM8LX3LDcS_1753768395","job_id":"","compressed_files":6,"zip_file":"pdf_merges/recommendations_2_114_2025-07-29_13-53-15.zip","download_url":"https://rec-letter.test/api/public/pdf-download/merge_DQG0rcTM8LX3LDcS_1753768395","total_execution_time":0.36304688453674316,"memory_peak":4194304,"step":"job_complete"} 
[2025-07-29 13:53:15] local.INFO: 📋 PDF壓縮任務已派發 {"task_id":"merge_DQG0rcTM8LX3LDcS_1753768395","queue_connection":"sync","execution_mode":"immediate (sync)"} 
[2025-07-29 13:53:15] local.INFO: 📋 PDF壓縮任務已啟動 {"task_id":"merge_DQG0rcTM8LX3LDcS_1753768395"} 
[2025-07-29 13:53:21] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-29 13:53:21] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_DQG0rcTM8LX3LDcS_1753768395"},"input":{"task_id":"merge_DQG0rcTM8LX3LDcS_1753768395"},"ip":"127.0.0.1"} 
[2025-07-29 13:53:21] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_DQG0rcTM8LX3LDcS_1753768395","status":"ready","message":"任務完成，可以下載","ip":"127.0.0.1"} 
[2025-07-29 13:53:21] local.INFO: 公共PDF壓縮檔案下載 {"task_id":"merge_DQG0rcTM8LX3LDcS_1753768395","file_path":"pdf_merges/recommendations_2_114_2025-07-29_13-53-15.zip","client_ip":"127.0.0.1","user_agent":"GuzzleHttp/7"} 
[2025-07-29 14:12:11] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-29 14:12:11] local.INFO: PDF壓縮任務執行 {"task_id":"merge_fwyfqA6PzvHV7KE4_1753769531","queue":"sync"} 
[2025-07-29 14:12:11] local.INFO: 🚀 PDF壓縮Job開始執行 {"task_id":"merge_fwyfqA6PzvHV7KE4_1753769531","job_id":"","queue":"sync","attempts":1,"max_tries":3,"timeout":1800,"parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-29T06:12:11.316438Z","client_ip":"127.0.0.1"},"memory_usage":4194304,"memory_peak":4194304,"timestamp":"2025-07-29T06:12:11.419010Z"} 
[2025-07-29 14:12:11] local.INFO: 📋 開始處理PDF壓縮任務 {"task_id":"merge_fwyfqA6PzvHV7KE4_1753769531","job_id":"","task_status":"processing","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-29T06:12:11.316438Z","client_ip":"127.0.0.1"},"step":"initialization"} 
[2025-07-29 14:12:11] local.INFO: 📂 步驟1: 獲取推薦函資料 {"task_id":"merge_fwyfqA6PzvHV7KE4_1753769531","step":"get_recommendations","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-29T06:12:11.316438Z","client_ip":"127.0.0.1"}} 
[2025-07-29 14:12:11] local.INFO: ✅ 成功獲取推薦函 {"task_id":"merge_fwyfqA6PzvHV7KE4_1753769531","recommendation_count":6,"step":"get_recommendations_success"} 
[2025-07-29 14:12:11] local.INFO: 👥 步驟2: 按考生分組 {"task_id":"merge_fwyfqA6PzvHV7KE4_1753769531","step":"group_by_applicant"} 
[2025-07-29 14:12:11] local.INFO: ✅ 考生分組完成 {"task_id":"merge_fwyfqA6PzvHV7KE4_1753769531","applicant_count":3,"step":"group_by_applicant_success"} 
[2025-07-29 14:12:11] local.INFO: 📁 步驟3: 準備檔案列表 {"task_id":"merge_fwyfqA6PzvHV7KE4_1753769531","step":"prepare_files","applicant_count":3} 
[2025-07-29 14:12:11] local.DEBUG: 🔄 處理考生檔案 {"task_id":"merge_fwyfqA6PzvHV7KE4_1753769531","applicant_id":348,"recommendation_count":2,"progress":1,"total":3} 
[2025-07-29 14:12:11] local.DEBUG: ✅ 考生檔案準備完成 {"task_id":"merge_fwyfqA6PzvHV7KE4_1753769531","applicant_id":348,"file_count":2} 
[2025-07-29 14:12:11] local.DEBUG: 🔄 處理考生檔案 {"task_id":"merge_fwyfqA6PzvHV7KE4_1753769531","applicant_id":349,"recommendation_count":2,"progress":2,"total":3} 
[2025-07-29 14:12:11] local.DEBUG: ✅ 考生檔案準備完成 {"task_id":"merge_fwyfqA6PzvHV7KE4_1753769531","applicant_id":349,"file_count":2} 
[2025-07-29 14:12:11] local.DEBUG: 🔄 處理考生檔案 {"task_id":"merge_fwyfqA6PzvHV7KE4_1753769531","applicant_id":351,"recommendation_count":2,"progress":3,"total":3} 
[2025-07-29 14:12:11] local.DEBUG: ✅ 考生檔案準備完成 {"task_id":"merge_fwyfqA6PzvHV7KE4_1753769531","applicant_id":351,"file_count":2} 
[2025-07-29 14:12:11] local.INFO: 📊 檔案準備統計 {"task_id":"merge_fwyfqA6PzvHV7KE4_1753769531","total_files":6,"processed_applicants":3,"step":"prepare_files_complete"} 
[2025-07-29 14:12:11] local.INFO: 🗜️ 步驟4: 開始建立ZIP檔案 {"task_id":"merge_fwyfqA6PzvHV7KE4_1753769531","step":"create_zip","file_count":6,"memory_usage":4194304} 
[2025-07-29 14:12:11] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1.pdf","zip_path":"348/1.pdf","file_size":30293} 
[2025-07-29 14:12:11] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/2.pdf","zip_path":"348/2.pdf","file_size":277531} 
[2025-07-29 14:12:11] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/349/1.pdf","zip_path":"349/1.pdf","file_size":30293} 
[2025-07-29 14:12:11] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/349/2.pdf","zip_path":"349/2.pdf","file_size":277531} 
[2025-07-29 14:12:11] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/351/1.pdf","zip_path":"351/1.pdf","file_size":30293} 
[2025-07-29 14:12:11] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/351/2.pdf","zip_path":"351/2.pdf","file_size":277531} 
[2025-07-29 14:12:11] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-29 14:12:11] local.INFO: ZIP檔案創建成功 {"task_id":"merge_fwyfqA6PzvHV7KE4_1753769531","zip_path":"pdf_merges/recommendations_2_114_2025-07-29_14-12-11.zip","full_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/public\\pdf_merges/recommendations_2_114_2025-07-29_14-12-11.zip","file_count":6,"file_size":883603} 
[2025-07-29 14:12:11] local.INFO: ✅ ZIP檔案建立完成 {"task_id":"merge_fwyfqA6PzvHV7KE4_1753769531","zip_file_path":"pdf_merges/recommendations_2_114_2025-07-29_14-12-11.zip","step":"create_zip_success"} 
[2025-07-29 14:12:11] local.INFO: 🔗 步驟5: 生成下載URL {"task_id":"merge_fwyfqA6PzvHV7KE4_1753769531","step":"generate_download_url"} 
[2025-07-29 14:12:11] local.INFO: ✅ 下載URL生成完成 {"task_id":"merge_fwyfqA6PzvHV7KE4_1753769531","download_url":"https://rec-letter.test/api/public/pdf-download/merge_fwyfqA6PzvHV7KE4_1753769531","step":"generate_download_url_success"} 
[2025-07-29 14:12:11] local.INFO: 🏁 步驟6: 標記任務完成 {"task_id":"merge_fwyfqA6PzvHV7KE4_1753769531","step":"mark_as_ready"} 
[2025-07-29 14:12:11] local.INFO: 🎉 PDF壓縮任務完成 {"task_id":"merge_fwyfqA6PzvHV7KE4_1753769531","job_id":"","compressed_files":6,"zip_file":"pdf_merges/recommendations_2_114_2025-07-29_14-12-11.zip","download_url":"https://rec-letter.test/api/public/pdf-download/merge_fwyfqA6PzvHV7KE4_1753769531","total_execution_time":0.5246238708496094,"memory_peak":4194304,"step":"job_complete"} 
[2025-07-29 14:12:11] local.INFO: 📋 PDF壓縮任務已派發 {"task_id":"merge_fwyfqA6PzvHV7KE4_1753769531","queue_connection":"sync","execution_mode":"immediate (sync)"} 
[2025-07-29 14:12:11] local.INFO: 📋 PDF壓縮任務已啟動 {"task_id":"merge_fwyfqA6PzvHV7KE4_1753769531"} 
[2025-07-29 14:12:17] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-29 14:12:17] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_fwyfqA6PzvHV7KE4_1753769531"},"input":{"task_id":"merge_fwyfqA6PzvHV7KE4_1753769531"},"ip":"127.0.0.1"} 
[2025-07-29 14:12:17] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_fwyfqA6PzvHV7KE4_1753769531","status":"ready","message":"任務完成，可以下載","ip":"127.0.0.1"} 
[2025-07-29 14:12:17] local.INFO: 公共PDF壓縮檔案下載 {"task_id":"merge_fwyfqA6PzvHV7KE4_1753769531","file_path":"pdf_merges/recommendations_2_114_2025-07-29_14-12-11.zip","client_ip":"127.0.0.1","user_agent":"GuzzleHttp/7"} 
[2025-07-29 14:15:30] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-29 14:15:30] local.INFO: PDF壓縮任務執行 {"task_id":"merge_x32VR1ZqjRTdF33w_1753769730","queue":"sync"} 
[2025-07-29 14:15:30] local.INFO: 🚀 PDF壓縮Job開始執行 {"task_id":"merge_x32VR1ZqjRTdF33w_1753769730","job_id":"","queue":"sync","attempts":1,"max_tries":3,"timeout":1800,"parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-29T06:15:30.281892Z","client_ip":"127.0.0.1"},"memory_usage":4194304,"memory_peak":4194304,"timestamp":"2025-07-29T06:15:30.340844Z"} 
[2025-07-29 14:15:30] local.INFO: 📋 開始處理PDF壓縮任務 {"task_id":"merge_x32VR1ZqjRTdF33w_1753769730","job_id":"","task_status":"processing","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-29T06:15:30.281892Z","client_ip":"127.0.0.1"},"step":"initialization"} 
[2025-07-29 14:15:30] local.INFO: 📂 步驟1: 獲取推薦函資料 {"task_id":"merge_x32VR1ZqjRTdF33w_1753769730","step":"get_recommendations","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-29T06:15:30.281892Z","client_ip":"127.0.0.1"}} 
[2025-07-29 14:15:30] local.INFO: ✅ 成功獲取推薦函 {"task_id":"merge_x32VR1ZqjRTdF33w_1753769730","recommendation_count":6,"step":"get_recommendations_success"} 
[2025-07-29 14:15:30] local.INFO: 👥 步驟2: 按考生分組 {"task_id":"merge_x32VR1ZqjRTdF33w_1753769730","step":"group_by_applicant"} 
[2025-07-29 14:15:30] local.INFO: ✅ 考生分組完成 {"task_id":"merge_x32VR1ZqjRTdF33w_1753769730","applicant_count":3,"step":"group_by_applicant_success"} 
[2025-07-29 14:15:30] local.INFO: 📁 步驟3: 準備檔案列表 {"task_id":"merge_x32VR1ZqjRTdF33w_1753769730","step":"prepare_files","applicant_count":3} 
[2025-07-29 14:15:30] local.DEBUG: 🔄 處理考生檔案 {"task_id":"merge_x32VR1ZqjRTdF33w_1753769730","applicant_id":348,"recommendation_count":2,"progress":1,"total":3} 
[2025-07-29 14:15:30] local.DEBUG: ✅ 考生檔案準備完成 {"task_id":"merge_x32VR1ZqjRTdF33w_1753769730","applicant_id":348,"file_count":2} 
[2025-07-29 14:15:30] local.DEBUG: 🔄 處理考生檔案 {"task_id":"merge_x32VR1ZqjRTdF33w_1753769730","applicant_id":349,"recommendation_count":2,"progress":2,"total":3} 
[2025-07-29 14:15:30] local.DEBUG: ✅ 考生檔案準備完成 {"task_id":"merge_x32VR1ZqjRTdF33w_1753769730","applicant_id":349,"file_count":2} 
[2025-07-29 14:15:30] local.DEBUG: 🔄 處理考生檔案 {"task_id":"merge_x32VR1ZqjRTdF33w_1753769730","applicant_id":351,"recommendation_count":2,"progress":3,"total":3} 
[2025-07-29 14:15:30] local.DEBUG: ✅ 考生檔案準備完成 {"task_id":"merge_x32VR1ZqjRTdF33w_1753769730","applicant_id":351,"file_count":2} 
[2025-07-29 14:15:30] local.INFO: 📊 檔案準備統計 {"task_id":"merge_x32VR1ZqjRTdF33w_1753769730","total_files":6,"processed_applicants":3,"step":"prepare_files_complete"} 
[2025-07-29 14:15:30] local.INFO: 🗜️ 步驟4: 開始建立ZIP檔案 {"task_id":"merge_x32VR1ZqjRTdF33w_1753769730","step":"create_zip","file_count":6,"memory_usage":4194304} 
[2025-07-29 14:15:30] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1.pdf","zip_path":"348/1.pdf","file_size":524288000} 
[2025-07-29 14:15:30] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/2.pdf","zip_path":"348/2.pdf","file_size":524288000} 
[2025-07-29 14:15:30] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/349/1.pdf","zip_path":"349/1.pdf","file_size":524288000} 
[2025-07-29 14:15:30] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/349/2.pdf","zip_path":"349/2.pdf","file_size":524288000} 
[2025-07-29 14:15:30] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/351/1.pdf","zip_path":"351/1.pdf","file_size":524288000} 
[2025-07-29 14:15:30] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/351/2.pdf","zip_path":"351/2.pdf","file_size":524288000} 
[2025-07-29 14:15:30] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-29 14:15:59] local.INFO: ZIP檔案創建成功 {"task_id":"merge_x32VR1ZqjRTdF33w_1753769730","zip_path":"pdf_merges/recommendations_2_114_2025-07-29_14-15-30.zip","full_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/public\\pdf_merges/recommendations_2_114_2025-07-29_14-15-30.zip","file_count":6,"file_size":3054350} 
[2025-07-29 14:15:59] local.INFO: ✅ ZIP檔案建立完成 {"task_id":"merge_x32VR1ZqjRTdF33w_1753769730","zip_file_path":"pdf_merges/recommendations_2_114_2025-07-29_14-15-30.zip","step":"create_zip_success"} 
[2025-07-29 14:15:59] local.INFO: 🔗 步驟5: 生成下載URL {"task_id":"merge_x32VR1ZqjRTdF33w_1753769730","step":"generate_download_url"} 
[2025-07-29 14:15:59] local.INFO: ✅ 下載URL生成完成 {"task_id":"merge_x32VR1ZqjRTdF33w_1753769730","download_url":"https://rec-letter.test/api/public/pdf-download/merge_x32VR1ZqjRTdF33w_1753769730","step":"generate_download_url_success"} 
[2025-07-29 14:15:59] local.INFO: 🏁 步驟6: 標記任務完成 {"task_id":"merge_x32VR1ZqjRTdF33w_1753769730","step":"mark_as_ready"} 
[2025-07-29 14:15:59] local.INFO: 🎉 PDF壓縮任務完成 {"task_id":"merge_x32VR1ZqjRTdF33w_1753769730","job_id":"","compressed_files":6,"zip_file":"pdf_merges/recommendations_2_114_2025-07-29_14-15-30.zip","download_url":"https://rec-letter.test/api/public/pdf-download/merge_x32VR1ZqjRTdF33w_1753769730","total_execution_time":29.39188814163208,"memory_peak":4194304,"step":"job_complete"} 
[2025-07-29 14:15:59] local.INFO: 📋 PDF壓縮任務已派發 {"task_id":"merge_x32VR1ZqjRTdF33w_1753769730","queue_connection":"sync","execution_mode":"immediate (sync)"} 
[2025-07-29 14:15:59] local.INFO: 📋 PDF壓縮任務已啟動 {"task_id":"merge_x32VR1ZqjRTdF33w_1753769730"} 
[2025-07-29 14:16:05] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-29 14:16:05] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_x32VR1ZqjRTdF33w_1753769730"},"input":{"task_id":"merge_x32VR1ZqjRTdF33w_1753769730"},"ip":"127.0.0.1"} 
[2025-07-29 14:16:05] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_x32VR1ZqjRTdF33w_1753769730","status":"ready","message":"任務完成，可以下載","ip":"127.0.0.1"} 
[2025-07-29 14:16:05] local.INFO: 公共PDF壓縮檔案下載 {"task_id":"merge_x32VR1ZqjRTdF33w_1753769730","file_path":"pdf_merges/recommendations_2_114_2025-07-29_14-15-30.zip","client_ip":"127.0.0.1","user_agent":"GuzzleHttp/7"} 
[2025-07-29 14:58:57] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-29 14:58:57] local.INFO: [TEST] 啟動測試模式 {"test_mode":"slow_processing","test_file_count":"default","test_delay_seconds":"default","client_ip":"127.0.0.1"} 
[2025-07-29 14:58:57] local.INFO: [TEST] 生成測試數據 {"test_mode":"slow_processing","file_count":20} 
[2025-07-29 14:58:57] local.INFO: [TASK] PDF壓縮任務執行 {"task_id":"merge_mPnrRpzkcMpNeSt2_1753772337","queue":"sync","test_mode":"slow_processing"} 
[2025-07-29 14:58:57] local.INFO: [START] PDF壓縮Job開始執行 {"task_id":"merge_mPnrRpzkcMpNeSt2_1753772337","job_id":"","queue":"sync","attempts":1,"max_tries":3,"timeout":1800,"parameters":{"exam_id":"2","exam_year":"114","test_mode":"slow_processing","requested_at":"2025-07-29T06:58:57.366707Z","client_ip":"127.0.0.1"},"memory_usage":4194304,"memory_peak":4194304,"timestamp":"2025-07-29T06:58:57.439686Z"} 
[2025-07-29 14:58:57] local.INFO: [PROCESS] 開始處理PDF壓縮任務 {"task_id":"merge_mPnrRpzkcMpNeSt2_1753772337","job_id":"","task_status":"processing","parameters":{"exam_id":"2","exam_year":"114","test_mode":"slow_processing","requested_at":"2025-07-29T06:58:57.366707Z","client_ip":"127.0.0.1"},"test_mode":"slow_processing","step":"initialization"} 
[2025-07-29 14:58:57] local.INFO: [STEP1] 獲取推薦函資料 {"task_id":"merge_mPnrRpzkcMpNeSt2_1753772337","step":"get_recommendations","test_mode":"slow_processing","parameters":{"exam_id":"2","exam_year":"114","test_mode":"slow_processing","requested_at":"2025-07-29T06:58:57.366707Z","client_ip":"127.0.0.1"}} 
[2025-07-29 14:58:57] local.INFO: [TEST] 生成測試推薦函數據 {"task_id":"merge_mPnrRpzkcMpNeSt2_1753772337","test_mode":"slow_processing","file_count":10} 
[2025-07-29 14:58:57] local.INFO: [SUCCESS] 成功獲取推薦函 {"task_id":"merge_mPnrRpzkcMpNeSt2_1753772337","recommendation_count":10,"step":"get_recommendations_success"} 
[2025-07-29 14:58:57] local.INFO: [STEP2] 按考生分組 {"task_id":"merge_mPnrRpzkcMpNeSt2_1753772337","step":"group_by_applicant"} 
[2025-07-29 14:58:57] local.INFO: [SUCCESS] 考生分組完成 {"task_id":"merge_mPnrRpzkcMpNeSt2_1753772337","applicant_count":4,"step":"group_by_applicant_success"} 
[2025-07-29 14:58:57] local.INFO: [STEP3] 準備檔案列表 {"task_id":"merge_mPnrRpzkcMpNeSt2_1753772337","step":"prepare_files","applicant_count":4} 
[2025-07-29 14:58:57] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_mPnrRpzkcMpNeSt2_1753772337","applicant_id":"test_autono_1","recommendation_count":3,"progress":1,"total":4} 
[2025-07-29 14:58:57] local.ERROR: [FINAL_FAILED] PDF壓縮Job最終失敗 {"task_id":"merge_mPnrRpzkcMpNeSt2_1753772337","job_id":"","exception_class":"Error","error":"Cannot use object of type stdClass as array","error_code":0,"error_file":"C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Jobs\\ProcessPdfMergeJob.php","error_line":348,"trace":"#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Jobs\\ProcessPdfMergeJob.php(186): App\\Jobs\\ProcessPdfMergeJob->prepareApplicantFiles(Array)
#1 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Jobs\\ProcessPdfMergeJob->handle()
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(132): Illuminate\\Container\\Container->call(Array)
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessPdfMergeJob))
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessPdfMergeJob))
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(136): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(125): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessPdfMergeJob), false)
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessPdfMergeJob))
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessPdfMergeJob))
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(120): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(68): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\SyncJob), Object(App\\Jobs\\ProcessPdfMergeJob))
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\SyncJob), Array)
#16 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SyncQueue.php(130): Illuminate\\Queue\\Jobs\\Job->fire()
#17 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SyncQueue.php(110): Illuminate\\Queue\\SyncQueue->executeJob(Object(App\\Jobs\\ProcessPdfMergeJob), '', NULL)
#18 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(250): Illuminate\\Queue\\SyncQueue->push(Object(App\\Jobs\\ProcessPdfMergeJob), '', NULL)
#19 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(234): Illuminate\\Bus\\Dispatcher->pushCommandToQueue(Object(Illuminate\\Queue\\SyncQueue), Object(App\\Jobs\\ProcessPdfMergeJob))
#20 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(83): Illuminate\\Bus\\Dispatcher->dispatchToQueue(Object(App\\Jobs\\ProcessPdfMergeJob))
#21 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bus\\PendingDispatch.php(221): Illuminate\\Bus\\Dispatcher->dispatch(Object(App\\Jobs\\ProcessPdfMergeJob))
#22 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bus\\Dispatchable.php(19): Illuminate\\Foundation\\Bus\\PendingDispatch->__destruct()
#23 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Controllers\\Api\\PdfMergeApiController.php(128): App\\Jobs\\ProcessPdfMergeJob::dispatch('merge_mPnrRpzkc...', Array)
#24 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\PdfMergeApiController->startMerge(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\PdfMergeApiController), 'startMerge')
#26 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#27 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#28 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\ApiWhitelistMiddleware.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ApiWhitelistMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#37 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#60 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Desktop\\rec-letter\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#63 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('C:\\\\Users\\\\<USER>\\\\D...')
#64 {main}","attempts":1,"max_tries":3,"memory_usage":4194304,"memory_peak":4194304,"parameters":{"exam_id":"2","exam_year":"114","test_mode":"slow_processing","requested_at":"2025-07-29T06:58:57.366707Z","client_ip":"127.0.0.1"},"timestamp":"2025-07-29T06:58:57.505278Z","step":"job_failed_final"} 
[2025-07-29 14:58:57] local.INFO: [UPDATE] 任務狀態已更新為失敗 {"task_id":"merge_mPnrRpzkcMpNeSt2_1753772337","task_status":"failed"} 
[2025-07-29 14:58:57] local.ERROR: Cannot use object of type stdClass as array {"exception":"[object] (Error(code: 0): Cannot use object of type stdClass as array at C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Jobs\\ProcessPdfMergeJob.php:348)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Jobs\\ProcessPdfMergeJob.php(186): App\\Jobs\\ProcessPdfMergeJob->prepareApplicantFiles(Array)
#1 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Jobs\\ProcessPdfMergeJob->handle()
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(132): Illuminate\\Container\\Container->call(Array)
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessPdfMergeJob))
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessPdfMergeJob))
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(136): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(125): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessPdfMergeJob), false)
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessPdfMergeJob))
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessPdfMergeJob))
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(120): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(68): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\SyncJob), Object(App\\Jobs\\ProcessPdfMergeJob))
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\SyncJob), Array)
#16 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SyncQueue.php(130): Illuminate\\Queue\\Jobs\\Job->fire()
#17 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SyncQueue.php(110): Illuminate\\Queue\\SyncQueue->executeJob(Object(App\\Jobs\\ProcessPdfMergeJob), '', NULL)
#18 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(250): Illuminate\\Queue\\SyncQueue->push(Object(App\\Jobs\\ProcessPdfMergeJob), '', NULL)
#19 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(234): Illuminate\\Bus\\Dispatcher->pushCommandToQueue(Object(Illuminate\\Queue\\SyncQueue), Object(App\\Jobs\\ProcessPdfMergeJob))
#20 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(83): Illuminate\\Bus\\Dispatcher->dispatchToQueue(Object(App\\Jobs\\ProcessPdfMergeJob))
#21 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bus\\PendingDispatch.php(221): Illuminate\\Bus\\Dispatcher->dispatch(Object(App\\Jobs\\ProcessPdfMergeJob))
#22 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bus\\Dispatchable.php(19): Illuminate\\Foundation\\Bus\\PendingDispatch->__destruct()
#23 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Controllers\\Api\\PdfMergeApiController.php(128): App\\Jobs\\ProcessPdfMergeJob::dispatch('merge_mPnrRpzkc...', Array)
#24 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\PdfMergeApiController->startMerge(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\PdfMergeApiController), 'startMerge')
#26 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#27 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#28 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\ApiWhitelistMiddleware.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ApiWhitelistMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#37 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#60 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Desktop\\rec-letter\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#63 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('C:\\\\Users\\\\<USER>\\\\D...')
#64 {main}
"} 
[2025-07-29 15:00:07] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-29 15:00:07] local.INFO: [TEST] 啟動測試模式 {"test_mode":"stress_test","test_file_count":"default","test_delay_seconds":"default","client_ip":"127.0.0.1"} 
[2025-07-29 15:00:07] local.INFO: [TEST] 生成測試數據 {"test_mode":"stress_test","file_count":100} 
[2025-07-29 15:00:07] local.INFO: [TASK] PDF壓縮任務執行 {"task_id":"merge_B7hrz3pWfTYNjj0u_1753772407","queue":"sync","test_mode":"stress_test"} 
[2025-07-29 15:00:07] local.INFO: [START] PDF壓縮Job開始執行 {"task_id":"merge_B7hrz3pWfTYNjj0u_1753772407","job_id":"","queue":"sync","attempts":1,"max_tries":3,"timeout":1800,"parameters":{"exam_id":"2","exam_year":"114","test_mode":"stress_test","requested_at":"2025-07-29T07:00:07.255275Z","client_ip":"127.0.0.1"},"memory_usage":4194304,"memory_peak":4194304,"timestamp":"2025-07-29T07:00:07.296706Z"} 
[2025-07-29 15:00:07] local.INFO: [PROCESS] 開始處理PDF壓縮任務 {"task_id":"merge_B7hrz3pWfTYNjj0u_1753772407","job_id":"","task_status":"processing","parameters":{"exam_id":"2","exam_year":"114","test_mode":"stress_test","requested_at":"2025-07-29T07:00:07.255275Z","client_ip":"127.0.0.1"},"test_mode":"stress_test","step":"initialization"} 
[2025-07-29 15:00:07] local.INFO: [STEP1] 獲取推薦函資料 {"task_id":"merge_B7hrz3pWfTYNjj0u_1753772407","step":"get_recommendations","test_mode":"stress_test","parameters":{"exam_id":"2","exam_year":"114","test_mode":"stress_test","requested_at":"2025-07-29T07:00:07.255275Z","client_ip":"127.0.0.1"}} 
[2025-07-29 15:00:07] local.INFO: [TEST] 生成測試推薦函數據 {"task_id":"merge_B7hrz3pWfTYNjj0u_1753772407","test_mode":"stress_test","file_count":10} 
[2025-07-29 15:00:07] local.INFO: [SUCCESS] 成功獲取推薦函 {"task_id":"merge_B7hrz3pWfTYNjj0u_1753772407","recommendation_count":10,"step":"get_recommendations_success"} 
[2025-07-29 15:00:07] local.INFO: [STEP2] 按考生分組 {"task_id":"merge_B7hrz3pWfTYNjj0u_1753772407","step":"group_by_applicant"} 
[2025-07-29 15:00:07] local.INFO: [SUCCESS] 考生分組完成 {"task_id":"merge_B7hrz3pWfTYNjj0u_1753772407","applicant_count":4,"step":"group_by_applicant_success"} 
[2025-07-29 15:00:07] local.INFO: [STEP3] 準備檔案列表 {"task_id":"merge_B7hrz3pWfTYNjj0u_1753772407","step":"prepare_files","applicant_count":4} 
[2025-07-29 15:00:07] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_B7hrz3pWfTYNjj0u_1753772407","applicant_id":"test_autono_1","recommendation_count":3,"progress":1,"total":4} 
[2025-07-29 15:00:07] local.ERROR: [FINAL_FAILED] PDF壓縮Job最終失敗 {"task_id":"merge_B7hrz3pWfTYNjj0u_1753772407","job_id":"","exception_class":"Error","error":"Cannot use object of type stdClass as array","error_code":0,"error_file":"C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Jobs\\ProcessPdfMergeJob.php","error_line":348,"trace":"#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Jobs\\ProcessPdfMergeJob.php(186): App\\Jobs\\ProcessPdfMergeJob->prepareApplicantFiles(Array)
#1 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Jobs\\ProcessPdfMergeJob->handle()
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(132): Illuminate\\Container\\Container->call(Array)
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessPdfMergeJob))
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessPdfMergeJob))
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(136): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(125): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessPdfMergeJob), false)
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessPdfMergeJob))
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessPdfMergeJob))
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(120): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(68): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\SyncJob), Object(App\\Jobs\\ProcessPdfMergeJob))
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\SyncJob), Array)
#16 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SyncQueue.php(130): Illuminate\\Queue\\Jobs\\Job->fire()
#17 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SyncQueue.php(110): Illuminate\\Queue\\SyncQueue->executeJob(Object(App\\Jobs\\ProcessPdfMergeJob), '', NULL)
#18 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(250): Illuminate\\Queue\\SyncQueue->push(Object(App\\Jobs\\ProcessPdfMergeJob), '', NULL)
#19 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(234): Illuminate\\Bus\\Dispatcher->pushCommandToQueue(Object(Illuminate\\Queue\\SyncQueue), Object(App\\Jobs\\ProcessPdfMergeJob))
#20 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(83): Illuminate\\Bus\\Dispatcher->dispatchToQueue(Object(App\\Jobs\\ProcessPdfMergeJob))
#21 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bus\\PendingDispatch.php(221): Illuminate\\Bus\\Dispatcher->dispatch(Object(App\\Jobs\\ProcessPdfMergeJob))
#22 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bus\\Dispatchable.php(19): Illuminate\\Foundation\\Bus\\PendingDispatch->__destruct()
#23 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Controllers\\Api\\PdfMergeApiController.php(128): App\\Jobs\\ProcessPdfMergeJob::dispatch('merge_B7hrz3pWf...', Array)
#24 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\PdfMergeApiController->startMerge(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\PdfMergeApiController), 'startMerge')
#26 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#27 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#28 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\ApiWhitelistMiddleware.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ApiWhitelistMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#37 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#60 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Desktop\\rec-letter\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#63 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('C:\\\\Users\\\\<USER>\\\\D...')
#64 {main}","attempts":1,"max_tries":3,"memory_usage":4194304,"memory_peak":4194304,"parameters":{"exam_id":"2","exam_year":"114","test_mode":"stress_test","requested_at":"2025-07-29T07:00:07.255275Z","client_ip":"127.0.0.1"},"timestamp":"2025-07-29T07:00:07.308157Z","step":"job_failed_final"} 
[2025-07-29 15:00:07] local.INFO: [UPDATE] 任務狀態已更新為失敗 {"task_id":"merge_B7hrz3pWfTYNjj0u_1753772407","task_status":"failed"} 
[2025-07-29 15:00:07] local.ERROR: Cannot use object of type stdClass as array {"exception":"[object] (Error(code: 0): Cannot use object of type stdClass as array at C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Jobs\\ProcessPdfMergeJob.php:348)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Jobs\\ProcessPdfMergeJob.php(186): App\\Jobs\\ProcessPdfMergeJob->prepareApplicantFiles(Array)
#1 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Jobs\\ProcessPdfMergeJob->handle()
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(132): Illuminate\\Container\\Container->call(Array)
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessPdfMergeJob))
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessPdfMergeJob))
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(136): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(125): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessPdfMergeJob), false)
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessPdfMergeJob))
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessPdfMergeJob))
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(120): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(68): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\SyncJob), Object(App\\Jobs\\ProcessPdfMergeJob))
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\SyncJob), Array)
#16 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SyncQueue.php(130): Illuminate\\Queue\\Jobs\\Job->fire()
#17 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SyncQueue.php(110): Illuminate\\Queue\\SyncQueue->executeJob(Object(App\\Jobs\\ProcessPdfMergeJob), '', NULL)
#18 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(250): Illuminate\\Queue\\SyncQueue->push(Object(App\\Jobs\\ProcessPdfMergeJob), '', NULL)
#19 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(234): Illuminate\\Bus\\Dispatcher->pushCommandToQueue(Object(Illuminate\\Queue\\SyncQueue), Object(App\\Jobs\\ProcessPdfMergeJob))
#20 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(83): Illuminate\\Bus\\Dispatcher->dispatchToQueue(Object(App\\Jobs\\ProcessPdfMergeJob))
#21 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bus\\PendingDispatch.php(221): Illuminate\\Bus\\Dispatcher->dispatch(Object(App\\Jobs\\ProcessPdfMergeJob))
#22 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bus\\Dispatchable.php(19): Illuminate\\Foundation\\Bus\\PendingDispatch->__destruct()
#23 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Controllers\\Api\\PdfMergeApiController.php(128): App\\Jobs\\ProcessPdfMergeJob::dispatch('merge_B7hrz3pWf...', Array)
#24 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\PdfMergeApiController->startMerge(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\PdfMergeApiController), 'startMerge')
#26 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#27 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#28 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\ApiWhitelistMiddleware.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ApiWhitelistMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#37 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#60 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Desktop\\rec-letter\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#63 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('C:\\\\Users\\\\<USER>\\\\D...')
#64 {main}
"} 
[2025-07-29 15:04:06] testing.INFO: [TEST] 生成測試推薦函數據 {"task_id":"test_task_123","test_mode":"stress_test","file_count":3} 
[2025-07-29 15:04:06] testing.INFO: [TEST] 創建測試PDF檔案 {"task_id":"test_task_123","recommendation_id":"test_rec_1","file_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_1.pdf","file_size":328} 
[2025-07-29 15:04:06] testing.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"test_task_123","recommendation_id":"test_rec_1","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_1.pdf","autono":"test_autono_1"} 
[2025-07-29 15:04:06] testing.INFO: [TEST] 創建測試PDF檔案 {"task_id":"test_task_123","recommendation_id":"test_rec_2","file_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_2.pdf","file_size":328} 
[2025-07-29 15:04:06] testing.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"test_task_123","recommendation_id":"test_rec_2","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_2.pdf","autono":"test_autono_1"} 
[2025-07-29 15:04:06] testing.INFO: [TEST] 創建測試PDF檔案 {"task_id":"test_task_123","recommendation_id":"test_rec_3","file_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_3.pdf","file_size":328} 
[2025-07-29 15:04:06] testing.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"test_task_123","recommendation_id":"test_rec_3","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_3.pdf","autono":"test_autono_1"} 
[2025-07-29 15:04:25] local.INFO: [TEST] 啟動測試模式 {"test_mode":"stress_test","test_file_count":5,"test_delay_seconds":"default","client_ip":null} 
[2025-07-29 15:04:25] local.INFO: [TEST] 生成測試數據 {"test_mode":"stress_test","file_count":5} 
[2025-07-29 15:04:25] local.INFO: [TASK] PDF壓縮任務執行 {"task_id":"merge_rt0tz2UQJjQ6ajZx_1753772665","queue":"sync","test_mode":"stress_test"} 
[2025-07-29 15:04:28] local.INFO: [START] PDF壓縮Job開始執行 {"task_id":"merge_rt0tz2UQJjQ6ajZx_1753772665","job_id":"","queue":"sync","attempts":1,"max_tries":3,"timeout":1800,"parameters":{"exam_id":"TEST2024","exam_year":113,"test_mode":"stress_test","test_file_count":5,"requested_at":"2025-07-29T07:04:25.611947Z"},"memory_usage":41943040,"memory_peak":41943040,"timestamp":"2025-07-29T07:04:28.046044Z"} 
[2025-07-29 15:04:28] local.INFO: [PROCESS] 開始處理PDF壓縮任務 {"task_id":"merge_rt0tz2UQJjQ6ajZx_1753772665","job_id":"","task_status":"processing","parameters":{"exam_id":"TEST2024","exam_year":113,"test_mode":"stress_test","test_file_count":5,"requested_at":"2025-07-29T07:04:25.611947Z"},"test_mode":"stress_test","step":"initialization"} 
[2025-07-29 15:04:28] local.INFO: [STEP1] 獲取推薦函資料 {"task_id":"merge_rt0tz2UQJjQ6ajZx_1753772665","step":"get_recommendations","test_mode":"stress_test","parameters":{"exam_id":"TEST2024","exam_year":113,"test_mode":"stress_test","test_file_count":5,"requested_at":"2025-07-29T07:04:25.611947Z"}} 
[2025-07-29 15:04:28] local.INFO: [TEST] 生成測試推薦函數據 {"task_id":"merge_rt0tz2UQJjQ6ajZx_1753772665","test_mode":"stress_test","file_count":5} 
[2025-07-29 15:04:28] local.INFO: [SUCCESS] 成功獲取推薦函 {"task_id":"merge_rt0tz2UQJjQ6ajZx_1753772665","recommendation_count":5,"step":"get_recommendations_success"} 
[2025-07-29 15:04:28] local.INFO: [STEP2] 按考生分組 {"task_id":"merge_rt0tz2UQJjQ6ajZx_1753772665","step":"group_by_applicant"} 
[2025-07-29 15:04:28] local.INFO: [SUCCESS] 考生分組完成 {"task_id":"merge_rt0tz2UQJjQ6ajZx_1753772665","applicant_count":2,"step":"group_by_applicant_success"} 
[2025-07-29 15:04:28] local.INFO: [STEP3] 準備檔案列表 {"task_id":"merge_rt0tz2UQJjQ6ajZx_1753772665","step":"prepare_files","applicant_count":2} 
[2025-07-29 15:04:28] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_rt0tz2UQJjQ6ajZx_1753772665","applicant_id":"test_autono_1","recommendation_count":3,"progress":1,"total":2} 
[2025-07-29 15:04:28] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_rt0tz2UQJjQ6ajZx_1753772665","recommendation_id":"test_rec_1","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_1.pdf","autono":"test_autono_1"} 
[2025-07-29 15:04:28] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_rt0tz2UQJjQ6ajZx_1753772665","recommendation_id":"test_rec_2","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_2.pdf","autono":"test_autono_1"} 
[2025-07-29 15:04:28] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_rt0tz2UQJjQ6ajZx_1753772665","recommendation_id":"test_rec_3","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_3.pdf","autono":"test_autono_1"} 
[2025-07-29 15:04:28] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_rt0tz2UQJjQ6ajZx_1753772665","applicant_id":"test_autono_1","file_count":3} 
[2025-07-29 15:04:28] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_rt0tz2UQJjQ6ajZx_1753772665","applicant_id":"test_autono_2","recommendation_count":2,"progress":2,"total":2} 
[2025-07-29 15:04:28] local.INFO: [TEST] 創建測試PDF檔案 {"task_id":"merge_rt0tz2UQJjQ6ajZx_1753772665","recommendation_id":"test_rec_4","file_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_4.pdf","file_size":328} 
[2025-07-29 15:04:28] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_rt0tz2UQJjQ6ajZx_1753772665","recommendation_id":"test_rec_4","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_4.pdf","autono":"test_autono_2"} 
[2025-07-29 15:04:28] local.INFO: [TEST] 創建測試PDF檔案 {"task_id":"merge_rt0tz2UQJjQ6ajZx_1753772665","recommendation_id":"test_rec_5","file_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_5.pdf","file_size":328} 
[2025-07-29 15:04:28] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_rt0tz2UQJjQ6ajZx_1753772665","recommendation_id":"test_rec_5","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_5.pdf","autono":"test_autono_2"} 
[2025-07-29 15:04:28] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_rt0tz2UQJjQ6ajZx_1753772665","applicant_id":"test_autono_2","file_count":2} 
[2025-07-29 15:04:28] local.INFO: [STATS] 檔案準備統計 {"task_id":"merge_rt0tz2UQJjQ6ajZx_1753772665","total_files":5,"processed_applicants":2,"step":"prepare_files_complete"} 
[2025-07-29 15:04:28] local.INFO: [STEP4] 開始建立ZIP檔案 {"task_id":"merge_rt0tz2UQJjQ6ajZx_1753772665","step":"create_zip","file_count":5,"memory_usage":41943040} 
[2025-07-29 15:04:29] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_1.pdf","zip_path":"test_autono_1/1.pdf","file_size":328} 
[2025-07-29 15:04:29] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_2.pdf","zip_path":"test_autono_1/2.pdf","file_size":328} 
[2025-07-29 15:04:29] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_3.pdf","zip_path":"test_autono_1/3.pdf","file_size":328} 
[2025-07-29 15:04:29] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_4.pdf","zip_path":"test_autono_2/1.pdf","file_size":328} 
[2025-07-29 15:04:29] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_5.pdf","zip_path":"test_autono_2/2.pdf","file_size":328} 
[2025-07-29 15:04:29] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-29 15:04:29] local.INFO: ZIP檔案創建成功 {"task_id":"merge_rt0tz2UQJjQ6ajZx_1753772665","zip_path":"pdf_merges/recommendations_TEST2024_113_2025-07-29_15-04-29.zip","full_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/public\\pdf_merges/recommendations_TEST2024_113_2025-07-29_15-04-29.zip","file_count":5,"file_size":2326} 
[2025-07-29 15:04:29] local.INFO: [SUCCESS] ZIP檔案建立完成 {"task_id":"merge_rt0tz2UQJjQ6ajZx_1753772665","zip_file_path":"pdf_merges/recommendations_TEST2024_113_2025-07-29_15-04-29.zip","step":"create_zip_success"} 
[2025-07-29 15:04:29] local.INFO: [STEP5] 生成下載URL {"task_id":"merge_rt0tz2UQJjQ6ajZx_1753772665","step":"generate_download_url"} 
[2025-07-29 15:04:29] local.INFO: [SUCCESS] 下載URL生成完成 {"task_id":"merge_rt0tz2UQJjQ6ajZx_1753772665","download_url":"http://rec-letter.test/api/public/pdf-download/merge_rt0tz2UQJjQ6ajZx_1753772665","step":"generate_download_url_success"} 
[2025-07-29 15:04:29] local.INFO: [STEP6] 標記任務完成 {"task_id":"merge_rt0tz2UQJjQ6ajZx_1753772665","step":"mark_as_ready"} 
[2025-07-29 15:04:29] local.INFO: [COMPLETE] PDF壓縮任務完成 {"task_id":"merge_rt0tz2UQJjQ6ajZx_1753772665","job_id":"","compressed_files":5,"zip_file":"pdf_merges/recommendations_TEST2024_113_2025-07-29_15-04-29.zip","download_url":"http://rec-letter.test/api/public/pdf-download/merge_rt0tz2UQJjQ6ajZx_1753772665","total_execution_time":13.068496942520142,"memory_peak":44040192,"step":"job_complete"} 
[2025-07-29 15:04:29] local.INFO: [DISPATCH] PDF壓縮任務已派發 {"task_id":"merge_rt0tz2UQJjQ6ajZx_1753772665","queue_connection":"sync","execution_mode":"immediate (sync)"} 
[2025-07-29 15:04:29] local.INFO: [START] PDF壓縮任務已啟動 {"task_id":"merge_rt0tz2UQJjQ6ajZx_1753772665"} 
[2025-07-29 15:05:28] local.INFO: [TEST] 生成測試推薦函數據 {"task_id":"test_fix_1753772728","test_mode":"stress_test","file_count":3} 
[2025-07-29 15:05:28] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"test_fix_1753772728","recommendation_id":"test_rec_1","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_1.pdf","autono":"test_autono_1"} 
[2025-07-29 15:05:28] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"test_fix_1753772728","recommendation_id":"test_rec_2","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_2.pdf","autono":"test_autono_1"} 
[2025-07-29 15:05:28] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"test_fix_1753772728","recommendation_id":"test_rec_3","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_3.pdf","autono":"test_autono_1"} 
[2025-07-29 15:06:33] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-29 15:06:33] local.INFO: [TEST] 啟動測試模式 {"test_mode":"stress_test","test_file_count":"default","test_delay_seconds":"default","client_ip":"127.0.0.1"} 
[2025-07-29 15:06:33] local.INFO: [TEST] 生成測試數據 {"test_mode":"stress_test","file_count":100} 
[2025-07-29 15:06:33] local.INFO: [TASK] PDF壓縮任務執行 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","queue":"sync","test_mode":"stress_test"} 
[2025-07-29 15:06:33] local.INFO: [START] PDF壓縮Job開始執行 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","job_id":"","queue":"sync","attempts":1,"max_tries":3,"timeout":1800,"parameters":{"exam_id":"2","exam_year":"114","test_mode":"stress_test","requested_at":"2025-07-29T07:06:33.302683Z","client_ip":"127.0.0.1"},"memory_usage":4194304,"memory_peak":4194304,"timestamp":"2025-07-29T07:06:33.366661Z"} 
[2025-07-29 15:06:33] local.INFO: [PROCESS] 開始處理PDF壓縮任務 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","job_id":"","task_status":"processing","parameters":{"exam_id":"2","exam_year":"114","test_mode":"stress_test","requested_at":"2025-07-29T07:06:33.302683Z","client_ip":"127.0.0.1"},"test_mode":"stress_test","step":"initialization"} 
[2025-07-29 15:06:33] local.INFO: [STEP1] 獲取推薦函資料 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","step":"get_recommendations","test_mode":"stress_test","parameters":{"exam_id":"2","exam_year":"114","test_mode":"stress_test","requested_at":"2025-07-29T07:06:33.302683Z","client_ip":"127.0.0.1"}} 
[2025-07-29 15:06:33] local.INFO: [TEST] 生成測試推薦函數據 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","test_mode":"stress_test","file_count":10} 
[2025-07-29 15:06:33] local.INFO: [SUCCESS] 成功獲取推薦函 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","recommendation_count":10,"step":"get_recommendations_success"} 
[2025-07-29 15:06:33] local.INFO: [STEP2] 按考生分組 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","step":"group_by_applicant"} 
[2025-07-29 15:06:33] local.INFO: [SUCCESS] 考生分組完成 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","applicant_count":4,"step":"group_by_applicant_success"} 
[2025-07-29 15:06:33] local.INFO: [STEP3] 準備檔案列表 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","step":"prepare_files","applicant_count":4} 
[2025-07-29 15:06:33] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","applicant_id":"test_autono_1","recommendation_count":3,"progress":1,"total":4} 
[2025-07-29 15:06:33] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","recommendation_id":"test_rec_1","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_1.pdf","autono":"test_autono_1"} 
[2025-07-29 15:06:33] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","recommendation_id":"test_rec_2","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_2.pdf","autono":"test_autono_1"} 
[2025-07-29 15:06:33] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","recommendation_id":"test_rec_3","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_3.pdf","autono":"test_autono_1"} 
[2025-07-29 15:06:33] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","applicant_id":"test_autono_1","file_count":3} 
[2025-07-29 15:06:33] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","applicant_id":"test_autono_2","recommendation_count":3,"progress":2,"total":4} 
[2025-07-29 15:06:33] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","recommendation_id":"test_rec_4","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_4.pdf","autono":"test_autono_2"} 
[2025-07-29 15:06:33] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","recommendation_id":"test_rec_5","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_5.pdf","autono":"test_autono_2"} 
[2025-07-29 15:06:33] local.INFO: [TEST] 創建測試PDF檔案 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","recommendation_id":"test_rec_6","file_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_6.pdf","file_size":328} 
[2025-07-29 15:06:33] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","recommendation_id":"test_rec_6","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_6.pdf","autono":"test_autono_2"} 
[2025-07-29 15:06:33] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","applicant_id":"test_autono_2","file_count":3} 
[2025-07-29 15:06:33] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","applicant_id":"test_autono_3","recommendation_count":3,"progress":3,"total":4} 
[2025-07-29 15:06:33] local.INFO: [TEST] 創建測試PDF檔案 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","recommendation_id":"test_rec_7","file_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_7.pdf","file_size":328} 
[2025-07-29 15:06:33] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","recommendation_id":"test_rec_7","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_7.pdf","autono":"test_autono_3"} 
[2025-07-29 15:06:33] local.INFO: [TEST] 創建測試PDF檔案 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","recommendation_id":"test_rec_8","file_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_8.pdf","file_size":328} 
[2025-07-29 15:06:33] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","recommendation_id":"test_rec_8","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_8.pdf","autono":"test_autono_3"} 
[2025-07-29 15:06:33] local.INFO: [TEST] 創建測試PDF檔案 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","recommendation_id":"test_rec_9","file_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_9.pdf","file_size":328} 
[2025-07-29 15:06:33] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","recommendation_id":"test_rec_9","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_9.pdf","autono":"test_autono_3"} 
[2025-07-29 15:06:33] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","applicant_id":"test_autono_3","file_count":3} 
[2025-07-29 15:06:33] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","applicant_id":"test_autono_4","recommendation_count":1,"progress":4,"total":4} 
[2025-07-29 15:06:33] local.INFO: [TEST] 創建測試PDF檔案 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","recommendation_id":"test_rec_10","file_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_10.pdf","file_size":328} 
[2025-07-29 15:06:33] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","recommendation_id":"test_rec_10","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_10.pdf","autono":"test_autono_4"} 
[2025-07-29 15:06:33] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","applicant_id":"test_autono_4","file_count":1} 
[2025-07-29 15:06:33] local.INFO: [STATS] 檔案準備統計 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","total_files":10,"processed_applicants":4,"step":"prepare_files_complete"} 
[2025-07-29 15:06:33] local.INFO: [STEP4] 開始建立ZIP檔案 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","step":"create_zip","file_count":10,"memory_usage":4194304} 
[2025-07-29 15:06:33] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_1.pdf","zip_path":"test_autono_1/1.pdf","file_size":328} 
[2025-07-29 15:06:33] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_2.pdf","zip_path":"test_autono_1/2.pdf","file_size":328} 
[2025-07-29 15:06:33] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_3.pdf","zip_path":"test_autono_1/3.pdf","file_size":328} 
[2025-07-29 15:06:33] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_4.pdf","zip_path":"test_autono_2/1.pdf","file_size":328} 
[2025-07-29 15:06:33] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_5.pdf","zip_path":"test_autono_2/2.pdf","file_size":328} 
[2025-07-29 15:06:33] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_6.pdf","zip_path":"test_autono_2/3.pdf","file_size":328} 
[2025-07-29 15:06:33] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_7.pdf","zip_path":"test_autono_3/1.pdf","file_size":328} 
[2025-07-29 15:06:33] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_8.pdf","zip_path":"test_autono_3/2.pdf","file_size":328} 
[2025-07-29 15:06:33] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_9.pdf","zip_path":"test_autono_3/3.pdf","file_size":328} 
[2025-07-29 15:06:33] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_10.pdf","zip_path":"test_autono_4/1.pdf","file_size":328} 
[2025-07-29 15:06:33] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-29 15:06:33] local.INFO: ZIP檔案創建成功 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","zip_path":"pdf_merges/recommendations_2_114_2025-07-29_15-06-33.zip","full_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/public\\pdf_merges/recommendations_2_114_2025-07-29_15-06-33.zip","file_count":10,"file_size":4042} 
[2025-07-29 15:06:33] local.INFO: [SUCCESS] ZIP檔案建立完成 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","zip_file_path":"pdf_merges/recommendations_2_114_2025-07-29_15-06-33.zip","step":"create_zip_success"} 
[2025-07-29 15:06:33] local.INFO: [STEP5] 生成下載URL {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","step":"generate_download_url"} 
[2025-07-29 15:06:33] local.INFO: [SUCCESS] 下載URL生成完成 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","download_url":"https://rec-letter.test/api/public/pdf-download/merge_7YX70msCnHGd8UUF_1753772793","step":"generate_download_url_success"} 
[2025-07-29 15:06:33] local.INFO: [STEP6] 標記任務完成 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","step":"mark_as_ready"} 
[2025-07-29 15:06:33] local.INFO: [COMPLETE] PDF壓縮任務完成 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","job_id":"","compressed_files":10,"zip_file":"pdf_merges/recommendations_2_114_2025-07-29_15-06-33.zip","download_url":"https://rec-letter.test/api/public/pdf-download/merge_7YX70msCnHGd8UUF_1753772793","total_execution_time":0.4982631206512451,"memory_peak":4194304,"step":"job_complete"} 
[2025-07-29 15:06:33] local.INFO: [DISPATCH] PDF壓縮任務已派發 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","queue_connection":"sync","execution_mode":"immediate (sync)"} 
[2025-07-29 15:06:33] local.INFO: [START] PDF壓縮任務已啟動 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793"} 
[2025-07-29 15:06:39] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-29 15:06:39] local.INFO: [REQUEST] 收到任務狀態查詢請求 {"query":{"task_id":"merge_7YX70msCnHGd8UUF_1753772793"},"input":{"task_id":"merge_7YX70msCnHGd8UUF_1753772793"},"ip":"127.0.0.1"} 
[2025-07-29 15:06:39] local.INFO: [RESPONSE] 回傳任務狀態 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","status":"ready","message":"任務完成，可以下載","ip":"127.0.0.1"} 
[2025-07-29 15:06:39] local.INFO: 公共PDF壓縮檔案下載 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","file_path":"pdf_merges/recommendations_2_114_2025-07-29_15-06-33.zip","client_ip":"127.0.0.1","user_agent":"GuzzleHttp/7"} 
[2025-07-29 15:07:10] local.ERROR: Maximum execution time of 30 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 30 seconds exceeded at C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Controllers\\Api\\PdfMergeApiController.php:347)
[stacktrace]
#0 {main}
"} 
[2025-07-29 15:07:15] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-29 15:07:15] local.INFO: [REQUEST] 收到任務狀態查詢請求 {"query":{"task_id":"merge_7YX70msCnHGd8UUF_1753772793"},"input":{"task_id":"merge_7YX70msCnHGd8UUF_1753772793"},"ip":"127.0.0.1"} 
[2025-07-29 15:07:15] local.INFO: [RESPONSE] 回傳任務狀態 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","status":"ready","message":"任務完成，可以下載","ip":"127.0.0.1"} 
[2025-07-29 15:07:15] local.INFO: 公共PDF壓縮檔案下載 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","file_path":"pdf_merges/recommendations_2_114_2025-07-29_15-06-33.zip","client_ip":"127.0.0.1","user_agent":"GuzzleHttp/7"} 
[2025-07-29 15:07:45] local.ERROR: Maximum execution time of 30 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 30 seconds exceeded at C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Controllers\\Api\\PdfMergeApiController.php:347)
[stacktrace]
#0 {main}
"} 
[2025-07-29 15:07:50] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-29 15:07:50] local.INFO: [REQUEST] 收到任務狀態查詢請求 {"query":{"task_id":"merge_7YX70msCnHGd8UUF_1753772793"},"input":{"task_id":"merge_7YX70msCnHGd8UUF_1753772793"},"ip":"127.0.0.1"} 
[2025-07-29 15:07:50] local.INFO: [RESPONSE] 回傳任務狀態 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","status":"ready","message":"任務完成，可以下載","ip":"127.0.0.1"} 
[2025-07-29 15:07:51] local.INFO: 公共PDF壓縮檔案下載 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","file_path":"pdf_merges/recommendations_2_114_2025-07-29_15-06-33.zip","client_ip":"127.0.0.1","user_agent":"GuzzleHttp/7"} 
[2025-07-29 15:08:21] local.ERROR: Maximum execution time of 30 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 30 seconds exceeded at C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Controllers\\Api\\PdfMergeApiController.php:347)
[stacktrace]
#0 {main}
"} 
[2025-07-29 15:08:26] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-29 15:08:26] local.INFO: [REQUEST] 收到任務狀態查詢請求 {"query":{"task_id":"merge_7YX70msCnHGd8UUF_1753772793"},"input":{"task_id":"merge_7YX70msCnHGd8UUF_1753772793"},"ip":"127.0.0.1"} 
[2025-07-29 15:08:26] local.INFO: [RESPONSE] 回傳任務狀態 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","status":"ready","message":"任務完成，可以下載","ip":"127.0.0.1"} 
[2025-07-29 15:08:26] local.INFO: 公共PDF壓縮檔案下載 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","file_path":"pdf_merges/recommendations_2_114_2025-07-29_15-06-33.zip","client_ip":"127.0.0.1","user_agent":"GuzzleHttp/7"} 
[2025-07-29 15:08:56] local.ERROR: Maximum execution time of 30 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 30 seconds exceeded at C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Controllers\\Api\\PdfMergeApiController.php:347)
[stacktrace]
#0 {main}
"} 
[2025-07-29 15:09:02] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-29 15:09:02] local.INFO: [REQUEST] 收到任務狀態查詢請求 {"query":{"task_id":"merge_7YX70msCnHGd8UUF_1753772793"},"input":{"task_id":"merge_7YX70msCnHGd8UUF_1753772793"},"ip":"127.0.0.1"} 
[2025-07-29 15:09:02] local.INFO: [RESPONSE] 回傳任務狀態 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","status":"ready","message":"任務完成，可以下載","ip":"127.0.0.1"} 
[2025-07-29 15:09:02] local.INFO: 公共PDF壓縮檔案下載 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","file_path":"pdf_merges/recommendations_2_114_2025-07-29_15-06-33.zip","client_ip":"127.0.0.1","user_agent":"GuzzleHttp/7"} 
[2025-07-29 15:09:32] local.ERROR: Maximum execution time of 30 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 30 seconds exceeded at C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Controllers\\Api\\PdfMergeApiController.php:347)
[stacktrace]
#0 {main}
"} 
[2025-07-29 15:09:37] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-29 15:09:37] local.INFO: [REQUEST] 收到任務狀態查詢請求 {"query":{"task_id":"merge_7YX70msCnHGd8UUF_1753772793"},"input":{"task_id":"merge_7YX70msCnHGd8UUF_1753772793"},"ip":"127.0.0.1"} 
[2025-07-29 15:09:37] local.INFO: [RESPONSE] 回傳任務狀態 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","status":"ready","message":"任務完成，可以下載","ip":"127.0.0.1"} 
[2025-07-29 15:09:38] local.INFO: 公共PDF壓縮檔案下載 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","file_path":"pdf_merges/recommendations_2_114_2025-07-29_15-06-33.zip","client_ip":"127.0.0.1","user_agent":"GuzzleHttp/7"} 
[2025-07-29 15:10:08] local.ERROR: Maximum execution time of 30 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 30 seconds exceeded at C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Controllers\\Api\\PdfMergeApiController.php:347)
[stacktrace]
#0 {main}
"} 
[2025-07-29 15:10:13] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-29 15:10:13] local.INFO: [REQUEST] 收到任務狀態查詢請求 {"query":{"task_id":"merge_7YX70msCnHGd8UUF_1753772793"},"input":{"task_id":"merge_7YX70msCnHGd8UUF_1753772793"},"ip":"127.0.0.1"} 
[2025-07-29 15:10:13] local.INFO: [RESPONSE] 回傳任務狀態 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","status":"ready","message":"任務完成，可以下載","ip":"127.0.0.1"} 
[2025-07-29 15:10:13] local.INFO: 公共PDF壓縮檔案下載 {"task_id":"merge_7YX70msCnHGd8UUF_1753772793","file_path":"pdf_merges/recommendations_2_114_2025-07-29_15-06-33.zip","client_ip":"127.0.0.1","user_agent":"GuzzleHttp/7"} 
[2025-07-29 15:11:22] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-29 15:11:22] local.INFO: [TEST] 啟動測試模式 {"test_mode":"large_files","test_file_count":"default","test_delay_seconds":"default","client_ip":"127.0.0.1"} 
[2025-07-29 15:11:22] local.INFO: [TEST] 生成測試數據 {"test_mode":"large_files","file_count":50} 
[2025-07-29 15:11:22] local.INFO: [TASK] PDF壓縮任務執行 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","queue":"sync","test_mode":"large_files"} 
[2025-07-29 15:11:23] local.INFO: [START] PDF壓縮Job開始執行 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","job_id":"","queue":"sync","attempts":1,"max_tries":3,"timeout":1800,"parameters":{"exam_id":"2","exam_year":"114","test_mode":"large_files","requested_at":"2025-07-29T07:11:22.970828Z","client_ip":"127.0.0.1"},"memory_usage":4194304,"memory_peak":4194304,"timestamp":"2025-07-29T07:11:23.014143Z"} 
[2025-07-29 15:11:23] local.INFO: [PROCESS] 開始處理PDF壓縮任務 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","job_id":"","task_status":"processing","parameters":{"exam_id":"2","exam_year":"114","test_mode":"large_files","requested_at":"2025-07-29T07:11:22.970828Z","client_ip":"127.0.0.1"},"test_mode":"large_files","step":"initialization"} 
[2025-07-29 15:11:23] local.INFO: [STEP1] 獲取推薦函資料 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","step":"get_recommendations","test_mode":"large_files","parameters":{"exam_id":"2","exam_year":"114","test_mode":"large_files","requested_at":"2025-07-29T07:11:22.970828Z","client_ip":"127.0.0.1"}} 
[2025-07-29 15:11:23] local.INFO: [TEST] 生成測試推薦函數據 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","test_mode":"large_files","file_count":10} 
[2025-07-29 15:11:23] local.INFO: [SUCCESS] 成功獲取推薦函 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","recommendation_count":10,"step":"get_recommendations_success"} 
[2025-07-29 15:11:23] local.INFO: [STEP2] 按考生分組 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","step":"group_by_applicant"} 
[2025-07-29 15:11:23] local.INFO: [SUCCESS] 考生分組完成 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","applicant_count":4,"step":"group_by_applicant_success"} 
[2025-07-29 15:11:23] local.INFO: [STEP3] 準備檔案列表 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","step":"prepare_files","applicant_count":4} 
[2025-07-29 15:11:23] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","applicant_id":"test_autono_1","recommendation_count":3,"progress":1,"total":4} 
[2025-07-29 15:11:23] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","recommendation_id":"test_rec_1","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_1.pdf","autono":"test_autono_1"} 
[2025-07-29 15:11:23] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","recommendation_id":"test_rec_2","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_2.pdf","autono":"test_autono_1"} 
[2025-07-29 15:11:23] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","recommendation_id":"test_rec_3","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_3.pdf","autono":"test_autono_1"} 
[2025-07-29 15:11:23] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","applicant_id":"test_autono_1","file_count":3} 
[2025-07-29 15:11:23] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","applicant_id":"test_autono_2","recommendation_count":3,"progress":2,"total":4} 
[2025-07-29 15:11:23] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","recommendation_id":"test_rec_4","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_4.pdf","autono":"test_autono_2"} 
[2025-07-29 15:11:23] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","recommendation_id":"test_rec_5","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_5.pdf","autono":"test_autono_2"} 
[2025-07-29 15:11:23] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","recommendation_id":"test_rec_6","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_6.pdf","autono":"test_autono_2"} 
[2025-07-29 15:11:23] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","applicant_id":"test_autono_2","file_count":3} 
[2025-07-29 15:11:23] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","applicant_id":"test_autono_3","recommendation_count":3,"progress":3,"total":4} 
[2025-07-29 15:11:23] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","recommendation_id":"test_rec_7","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_7.pdf","autono":"test_autono_3"} 
[2025-07-29 15:11:23] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","recommendation_id":"test_rec_8","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_8.pdf","autono":"test_autono_3"} 
[2025-07-29 15:11:23] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","recommendation_id":"test_rec_9","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_9.pdf","autono":"test_autono_3"} 
[2025-07-29 15:11:23] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","applicant_id":"test_autono_3","file_count":3} 
[2025-07-29 15:11:23] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","applicant_id":"test_autono_4","recommendation_count":1,"progress":4,"total":4} 
[2025-07-29 15:11:23] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","recommendation_id":"test_rec_10","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_10.pdf","autono":"test_autono_4"} 
[2025-07-29 15:11:23] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","applicant_id":"test_autono_4","file_count":1} 
[2025-07-29 15:11:23] local.INFO: [STATS] 檔案準備統計 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","total_files":10,"processed_applicants":4,"step":"prepare_files_complete"} 
[2025-07-29 15:11:23] local.INFO: [STEP4] 開始建立ZIP檔案 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","step":"create_zip","file_count":10,"memory_usage":4194304} 
[2025-07-29 15:11:23] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_1.pdf","zip_path":"test_autono_1/1.pdf","file_size":328} 
[2025-07-29 15:11:23] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_2.pdf","zip_path":"test_autono_1/2.pdf","file_size":328} 
[2025-07-29 15:11:23] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_3.pdf","zip_path":"test_autono_1/3.pdf","file_size":328} 
[2025-07-29 15:11:23] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_4.pdf","zip_path":"test_autono_2/1.pdf","file_size":328} 
[2025-07-29 15:11:23] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_5.pdf","zip_path":"test_autono_2/2.pdf","file_size":328} 
[2025-07-29 15:11:23] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_6.pdf","zip_path":"test_autono_2/3.pdf","file_size":328} 
[2025-07-29 15:11:23] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_7.pdf","zip_path":"test_autono_3/1.pdf","file_size":328} 
[2025-07-29 15:11:23] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_8.pdf","zip_path":"test_autono_3/2.pdf","file_size":328} 
[2025-07-29 15:11:23] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_9.pdf","zip_path":"test_autono_3/3.pdf","file_size":328} 
[2025-07-29 15:11:23] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_10.pdf","zip_path":"test_autono_4/1.pdf","file_size":328} 
[2025-07-29 15:11:23] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-29 15:11:23] local.INFO: ZIP檔案創建成功 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","zip_path":"pdf_merges/recommendations_2_114_2025-07-29_15-11-23.zip","full_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/public\\pdf_merges/recommendations_2_114_2025-07-29_15-11-23.zip","file_count":10,"file_size":4041} 
[2025-07-29 15:11:23] local.INFO: [SUCCESS] ZIP檔案建立完成 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","zip_file_path":"pdf_merges/recommendations_2_114_2025-07-29_15-11-23.zip","step":"create_zip_success"} 
[2025-07-29 15:11:23] local.INFO: [STEP5] 生成下載URL {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","step":"generate_download_url"} 
[2025-07-29 15:11:23] local.INFO: [SUCCESS] 下載URL生成完成 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","download_url":"https://rec-letter.test/api/public/pdf-download/merge_Uzv6b4lVADpL5qnC_1753773082","step":"generate_download_url_success"} 
[2025-07-29 15:11:23] local.INFO: [STEP6] 標記任務完成 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","step":"mark_as_ready"} 
[2025-07-29 15:11:23] local.INFO: [COMPLETE] PDF壓縮任務完成 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","job_id":"","compressed_files":10,"zip_file":"pdf_merges/recommendations_2_114_2025-07-29_15-11-23.zip","download_url":"https://rec-letter.test/api/public/pdf-download/merge_Uzv6b4lVADpL5qnC_1753773082","total_execution_time":0.22674298286437988,"memory_peak":4194304,"step":"job_complete"} 
[2025-07-29 15:11:23] local.INFO: [DISPATCH] PDF壓縮任務已派發 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","queue_connection":"sync","execution_mode":"immediate (sync)"} 
[2025-07-29 15:11:23] local.INFO: [START] PDF壓縮任務已啟動 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082"} 
[2025-07-29 15:11:28] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-29 15:11:29] local.INFO: [REQUEST] 收到任務狀態查詢請求 {"query":{"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082"},"input":{"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082"},"ip":"127.0.0.1"} 
[2025-07-29 15:11:29] local.INFO: [RESPONSE] 回傳任務狀態 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","status":"ready","message":"任務完成，可以下載","ip":"127.0.0.1"} 
[2025-07-29 15:11:29] local.INFO: 公共PDF壓縮檔案下載 {"task_id":"merge_Uzv6b4lVADpL5qnC_1753773082","file_path":"pdf_merges/recommendations_2_114_2025-07-29_15-11-23.zip","client_ip":"127.0.0.1","user_agent":"GuzzleHttp/7"} 
[2025-07-29 15:13:29] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-29 15:13:29] local.INFO: [TEST] 啟動測試模式 {"test_mode":"transmission_test","test_file_count":"default","test_delay_seconds":"default","client_ip":"127.0.0.1"} 
[2025-07-29 15:13:29] local.INFO: [TEST] 生成測試數據 {"test_mode":"transmission_test","file_count":30} 
[2025-07-29 15:13:29] local.INFO: [TRANSMISSION] 傳輸穩定性檢測通過 {"client_ip":"127.0.0.1"} 
[2025-07-29 15:13:29] local.INFO: [TASK] PDF壓縮任務執行 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","queue":"sync","test_mode":"transmission_test"} 
[2025-07-29 15:13:29] local.INFO: [START] PDF壓縮Job開始執行 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","job_id":"","queue":"sync","attempts":1,"max_tries":3,"timeout":1800,"parameters":{"exam_id":"2","exam_year":"114","test_mode":"transmission_test","requested_at":"2025-07-29T07:13:29.191463Z","client_ip":"127.0.0.1"},"memory_usage":4194304,"memory_peak":4194304,"timestamp":"2025-07-29T07:13:29.253333Z"} 
[2025-07-29 15:13:29] local.INFO: [PROCESS] 開始處理PDF壓縮任務 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","job_id":"","task_status":"processing","parameters":{"exam_id":"2","exam_year":"114","test_mode":"transmission_test","requested_at":"2025-07-29T07:13:29.191463Z","client_ip":"127.0.0.1"},"test_mode":"transmission_test","step":"initialization"} 
[2025-07-29 15:13:29] local.INFO: [STEP1] 獲取推薦函資料 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","step":"get_recommendations","test_mode":"transmission_test","parameters":{"exam_id":"2","exam_year":"114","test_mode":"transmission_test","requested_at":"2025-07-29T07:13:29.191463Z","client_ip":"127.0.0.1"}} 
[2025-07-29 15:13:29] local.INFO: [TEST] 生成測試推薦函數據 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","test_mode":"transmission_test","file_count":10} 
[2025-07-29 15:13:29] local.INFO: [SUCCESS] 成功獲取推薦函 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","recommendation_count":10,"step":"get_recommendations_success"} 
[2025-07-29 15:13:29] local.INFO: [STEP2] 按考生分組 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","step":"group_by_applicant"} 
[2025-07-29 15:13:29] local.INFO: [SUCCESS] 考生分組完成 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","applicant_count":4,"step":"group_by_applicant_success"} 
[2025-07-29 15:13:29] local.INFO: [STEP3] 準備檔案列表 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","step":"prepare_files","applicant_count":4} 
[2025-07-29 15:13:29] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","applicant_id":"test_autono_1","recommendation_count":3,"progress":1,"total":4} 
[2025-07-29 15:13:29] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","recommendation_id":"test_rec_1","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_1.pdf","autono":"test_autono_1"} 
[2025-07-29 15:13:29] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","recommendation_id":"test_rec_2","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_2.pdf","autono":"test_autono_1"} 
[2025-07-29 15:13:29] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","recommendation_id":"test_rec_3","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_3.pdf","autono":"test_autono_1"} 
[2025-07-29 15:13:29] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","applicant_id":"test_autono_1","file_count":3} 
[2025-07-29 15:13:29] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","applicant_id":"test_autono_2","recommendation_count":3,"progress":2,"total":4} 
[2025-07-29 15:13:29] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","recommendation_id":"test_rec_4","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_4.pdf","autono":"test_autono_2"} 
[2025-07-29 15:13:29] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","recommendation_id":"test_rec_5","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_5.pdf","autono":"test_autono_2"} 
[2025-07-29 15:13:29] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","recommendation_id":"test_rec_6","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_6.pdf","autono":"test_autono_2"} 
[2025-07-29 15:13:29] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","applicant_id":"test_autono_2","file_count":3} 
[2025-07-29 15:13:29] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","applicant_id":"test_autono_3","recommendation_count":3,"progress":3,"total":4} 
[2025-07-29 15:13:29] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","recommendation_id":"test_rec_7","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_7.pdf","autono":"test_autono_3"} 
[2025-07-29 15:13:29] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","recommendation_id":"test_rec_8","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_8.pdf","autono":"test_autono_3"} 
[2025-07-29 15:13:29] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","recommendation_id":"test_rec_9","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_9.pdf","autono":"test_autono_3"} 
[2025-07-29 15:13:29] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","applicant_id":"test_autono_3","file_count":3} 
[2025-07-29 15:13:29] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","applicant_id":"test_autono_4","recommendation_count":1,"progress":4,"total":4} 
[2025-07-29 15:13:29] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","recommendation_id":"test_rec_10","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_10.pdf","autono":"test_autono_4"} 
[2025-07-29 15:13:29] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","applicant_id":"test_autono_4","file_count":1} 
[2025-07-29 15:13:29] local.INFO: [STATS] 檔案準備統計 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","total_files":10,"processed_applicants":4,"step":"prepare_files_complete"} 
[2025-07-29 15:13:29] local.INFO: [STEP4] 開始建立ZIP檔案 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","step":"create_zip","file_count":10,"memory_usage":4194304} 
[2025-07-29 15:13:29] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_1.pdf","zip_path":"test_autono_1/1.pdf","file_size":328} 
[2025-07-29 15:13:29] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_2.pdf","zip_path":"test_autono_1/2.pdf","file_size":328} 
[2025-07-29 15:13:29] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_3.pdf","zip_path":"test_autono_1/3.pdf","file_size":328} 
[2025-07-29 15:13:29] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_4.pdf","zip_path":"test_autono_2/1.pdf","file_size":328} 
[2025-07-29 15:13:29] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_5.pdf","zip_path":"test_autono_2/2.pdf","file_size":328} 
[2025-07-29 15:13:29] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_6.pdf","zip_path":"test_autono_2/3.pdf","file_size":328} 
[2025-07-29 15:13:29] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_7.pdf","zip_path":"test_autono_3/1.pdf","file_size":328} 
[2025-07-29 15:13:29] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_8.pdf","zip_path":"test_autono_3/2.pdf","file_size":328} 
[2025-07-29 15:13:29] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_9.pdf","zip_path":"test_autono_3/3.pdf","file_size":328} 
[2025-07-29 15:13:29] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_10.pdf","zip_path":"test_autono_4/1.pdf","file_size":328} 
[2025-07-29 15:13:29] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-29 15:13:29] local.INFO: ZIP檔案創建成功 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","zip_path":"pdf_merges/recommendations_2_114_2025-07-29_15-13-29.zip","full_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/public\\pdf_merges/recommendations_2_114_2025-07-29_15-13-29.zip","file_count":10,"file_size":4041} 
[2025-07-29 15:13:29] local.INFO: [SUCCESS] ZIP檔案建立完成 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","zip_file_path":"pdf_merges/recommendations_2_114_2025-07-29_15-13-29.zip","step":"create_zip_success"} 
[2025-07-29 15:13:29] local.INFO: [STEP5] 生成下載URL {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","step":"generate_download_url"} 
[2025-07-29 15:13:29] local.INFO: [SUCCESS] 下載URL生成完成 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","download_url":"https://rec-letter.test/api/public/pdf-download/merge_39UrTWzz3I9Hsjwk_1753773209","step":"generate_download_url_success"} 
[2025-07-29 15:13:29] local.INFO: [STEP6] 標記任務完成 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","step":"mark_as_ready"} 
[2025-07-29 15:13:29] local.INFO: [COMPLETE] PDF壓縮任務完成 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","job_id":"","compressed_files":10,"zip_file":"pdf_merges/recommendations_2_114_2025-07-29_15-13-29.zip","download_url":"https://rec-letter.test/api/public/pdf-download/merge_39UrTWzz3I9Hsjwk_1753773209","total_execution_time":0.27164697647094727,"memory_peak":4194304,"step":"job_complete"} 
[2025-07-29 15:13:29] local.INFO: [DISPATCH] PDF壓縮任務已派發 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","queue_connection":"sync","execution_mode":"immediate (sync)"} 
[2025-07-29 15:13:29] local.INFO: [START] PDF壓縮任務已啟動 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209"} 
[2025-07-29 15:13:34] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-29 15:13:34] local.INFO: [REQUEST] 收到任務狀態查詢請求 {"query":{"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209"},"input":{"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209"},"ip":"127.0.0.1"} 
[2025-07-29 15:13:34] local.INFO: [RESPONSE] 回傳任務狀態 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","status":"ready","message":"任務完成，可以下載","ip":"127.0.0.1"} 
[2025-07-29 15:13:35] local.INFO: 公共PDF壓縮檔案下載 {"task_id":"merge_39UrTWzz3I9Hsjwk_1753773209","file_path":"pdf_merges/recommendations_2_114_2025-07-29_15-13-29.zip","client_ip":"127.0.0.1","user_agent":"GuzzleHttp/7"} 
[2025-07-29 15:19:37] testing.INFO: [TEST] 生成測試推薦函數據 {"task_id":"test_task_123","test_mode":"large_zip","file_count":1} 
[2025-07-29 15:19:37] testing.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"test_task_123","recommendation_id":"test_rec_1","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_1.pdf","autono":"test_autono_1"} 
[2025-07-29 15:20:42] local.INFO: [LARGE_ZIP] 開始創建3GB大型ZIP檔案 {"task_id":"large_zip_test_1753773642","target_size":"3GB"} 
[2025-07-29 15:20:42] local.INFO: [LARGE_ZIP] 測試模式：創建10MB檔案 {"task_id":"large_zip_test_1753773642"} 
[2025-07-29 15:20:42] local.INFO: [LARGE_ZIP] 開始寫入大型檔案 {"task_id":"large_zip_test_1753773642","target_size_bytes":10485760,"chunk_size_bytes":1048576} 
[2025-07-29 15:20:42] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"large_zip_test_1753773642","progress":"10%","written_bytes":1048576,"written_mb":1.0} 
[2025-07-29 15:20:42] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"large_zip_test_1753773642","progress":"20%","written_bytes":2097152,"written_mb":2.0} 
[2025-07-29 15:20:42] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"large_zip_test_1753773642","progress":"30%","written_bytes":3145728,"written_mb":3.0} 
[2025-07-29 15:20:42] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"large_zip_test_1753773642","progress":"40%","written_bytes":4194304,"written_mb":4.0} 
[2025-07-29 15:20:42] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"large_zip_test_1753773642","progress":"50%","written_bytes":5242880,"written_mb":5.0} 
[2025-07-29 15:20:42] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"large_zip_test_1753773642","progress":"60%","written_bytes":6291456,"written_mb":6.0} 
[2025-07-29 15:20:42] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"large_zip_test_1753773642","progress":"70%","written_bytes":7340032,"written_mb":7.0} 
[2025-07-29 15:20:42] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"large_zip_test_1753773642","progress":"80%","written_bytes":8388608,"written_mb":8.0} 
[2025-07-29 15:20:42] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"large_zip_test_1753773642","progress":"90%","written_bytes":9437184,"written_mb":9.0} 
[2025-07-29 15:20:42] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"large_zip_test_1753773642","progress":"100%","written_bytes":10485760,"written_mb":10.0} 
[2025-07-29 15:20:42] local.INFO: [LARGE_ZIP] 大型ZIP檔案創建完成 {"task_id":"large_zip_test_1753773642","file_path":"pdf_packages/large_test_large_zip_test_1753773642.zip","final_size_bytes":10485760,"final_size_gb":0.01} 
[2025-07-29 15:22:10] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-29 15:22:10] local.INFO: [TEST] 啟動測試模式 {"test_mode":"large_zip","test_file_count":"default","test_delay_seconds":"default","client_ip":"127.0.0.1"} 
[2025-07-29 15:22:10] local.INFO: [TEST] 生成測試數據 {"test_mode":"large_zip","file_count":1} 
[2025-07-29 15:22:10] local.INFO: [TASK] PDF壓縮任務執行 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","queue":"sync","test_mode":"large_zip"} 
[2025-07-29 15:22:10] local.INFO: [START] PDF壓縮Job開始執行 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","job_id":"","queue":"sync","attempts":1,"max_tries":3,"timeout":1800,"parameters":{"exam_id":"2","exam_year":"114","test_mode":"large_zip","requested_at":"2025-07-29T07:22:10.065560Z","client_ip":"127.0.0.1"},"memory_usage":4194304,"memory_peak":4194304,"timestamp":"2025-07-29T07:22:10.119649Z"} 
[2025-07-29 15:22:10] local.INFO: [PROCESS] 開始處理PDF壓縮任務 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","job_id":"","task_status":"processing","parameters":{"exam_id":"2","exam_year":"114","test_mode":"large_zip","requested_at":"2025-07-29T07:22:10.065560Z","client_ip":"127.0.0.1"},"test_mode":"large_zip","step":"initialization"} 
[2025-07-29 15:22:10] local.INFO: [STEP1] 獲取推薦函資料 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","step":"get_recommendations","test_mode":"large_zip","parameters":{"exam_id":"2","exam_year":"114","test_mode":"large_zip","requested_at":"2025-07-29T07:22:10.065560Z","client_ip":"127.0.0.1"}} 
[2025-07-29 15:22:10] local.INFO: [TEST] 生成測試推薦函數據 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","test_mode":"large_zip","file_count":10} 
[2025-07-29 15:22:10] local.INFO: [SUCCESS] 成功獲取推薦函 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","recommendation_count":10,"step":"get_recommendations_success"} 
[2025-07-29 15:22:10] local.INFO: [STEP2] 按考生分組 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","step":"group_by_applicant"} 
[2025-07-29 15:22:10] local.INFO: [SUCCESS] 考生分組完成 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","applicant_count":4,"step":"group_by_applicant_success"} 
[2025-07-29 15:22:10] local.INFO: [STEP3] 準備檔案列表 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","step":"prepare_files","applicant_count":4} 
[2025-07-29 15:22:10] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","applicant_id":"test_autono_1","recommendation_count":3,"progress":1,"total":4} 
[2025-07-29 15:22:10] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","recommendation_id":"test_rec_1","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_1.pdf","autono":"test_autono_1"} 
[2025-07-29 15:22:10] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","recommendation_id":"test_rec_2","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_2.pdf","autono":"test_autono_1"} 
[2025-07-29 15:22:10] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","recommendation_id":"test_rec_3","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_3.pdf","autono":"test_autono_1"} 
[2025-07-29 15:22:10] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","applicant_id":"test_autono_1","file_count":3} 
[2025-07-29 15:22:10] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","applicant_id":"test_autono_2","recommendation_count":3,"progress":2,"total":4} 
[2025-07-29 15:22:10] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","recommendation_id":"test_rec_4","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_4.pdf","autono":"test_autono_2"} 
[2025-07-29 15:22:10] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","recommendation_id":"test_rec_5","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_5.pdf","autono":"test_autono_2"} 
[2025-07-29 15:22:10] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","recommendation_id":"test_rec_6","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_6.pdf","autono":"test_autono_2"} 
[2025-07-29 15:22:10] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","applicant_id":"test_autono_2","file_count":3} 
[2025-07-29 15:22:10] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","applicant_id":"test_autono_3","recommendation_count":3,"progress":3,"total":4} 
[2025-07-29 15:22:10] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","recommendation_id":"test_rec_7","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_7.pdf","autono":"test_autono_3"} 
[2025-07-29 15:22:10] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","recommendation_id":"test_rec_8","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_8.pdf","autono":"test_autono_3"} 
[2025-07-29 15:22:10] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","recommendation_id":"test_rec_9","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_9.pdf","autono":"test_autono_3"} 
[2025-07-29 15:22:10] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","applicant_id":"test_autono_3","file_count":3} 
[2025-07-29 15:22:10] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","applicant_id":"test_autono_4","recommendation_count":1,"progress":4,"total":4} 
[2025-07-29 15:22:10] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","recommendation_id":"test_rec_10","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_10.pdf","autono":"test_autono_4"} 
[2025-07-29 15:22:10] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","applicant_id":"test_autono_4","file_count":1} 
[2025-07-29 15:22:10] local.INFO: [STATS] 檔案準備統計 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","total_files":10,"processed_applicants":4,"step":"prepare_files_complete"} 
[2025-07-29 15:22:10] local.INFO: [STEP4] 開始建立ZIP檔案 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","step":"create_zip","file_count":10,"test_mode":"large_zip","memory_usage":4194304} 
[2025-07-29 15:22:10] local.INFO: [LARGE_ZIP] 開始創建3GB大型ZIP檔案 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","target_size":"3GB"} 
[2025-07-29 15:22:10] local.INFO: [LARGE_ZIP] 開始寫入大型檔案 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","target_size_bytes":3221225472,"chunk_size_bytes":1048576} 
[2025-07-29 15:22:11] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","progress":"10%","written_bytes":322961408,"written_mb":308.0} 
[2025-07-29 15:22:13] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","progress":"20%","written_bytes":644874240,"written_mb":615.0} 
[2025-07-29 15:22:15] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","progress":"30%","written_bytes":966787072,"written_mb":922.0} 
[2025-07-29 15:22:16] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","progress":"40%","written_bytes":1288699904,"written_mb":1229.0} 
[2025-07-29 15:22:19] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","progress":"50%","written_bytes":1610612736,"written_mb":1536.0} 
[2025-07-29 15:22:20] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","progress":"60%","written_bytes":1933574144,"written_mb":1844.0} 
[2025-07-29 15:22:22] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","progress":"70%","written_bytes":2255486976,"written_mb":2151.0} 
[2025-07-29 15:22:23] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","progress":"80%","written_bytes":2577399808,"written_mb":2458.0} 
[2025-07-29 15:22:24] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","progress":"90%","written_bytes":2899312640,"written_mb":2765.0} 
[2025-07-29 15:22:25] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","progress":"100%","written_bytes":3221225472,"written_mb":3072.0} 
[2025-07-29 15:22:25] local.INFO: [LARGE_ZIP] 大型ZIP檔案創建完成 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","file_path":"pdf_packages/large_test_merge_EYwi90NqH3yKYQv2_1753773730.zip","final_size_bytes":3221225472,"final_size_gb":3.0} 
[2025-07-29 15:22:25] local.INFO: [SUCCESS] ZIP檔案建立完成 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","zip_file_path":"pdf_packages/large_test_merge_EYwi90NqH3yKYQv2_1753773730.zip","step":"create_zip_success"} 
[2025-07-29 15:22:25] local.INFO: [STEP5] 生成下載URL {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","step":"generate_download_url"} 
[2025-07-29 15:22:25] local.INFO: [SUCCESS] 下載URL生成完成 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","download_url":"https://rec-letter.test/api/public/pdf-download/merge_EYwi90NqH3yKYQv2_1753773730","step":"generate_download_url_success"} 
[2025-07-29 15:22:25] local.INFO: [STEP6] 標記任務完成 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","step":"mark_as_ready"} 
[2025-07-29 15:22:25] local.INFO: [COMPLETE] PDF壓縮任務完成 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","job_id":"","compressed_files":10,"zip_file":"pdf_packages/large_test_merge_EYwi90NqH3yKYQv2_1753773730.zip","download_url":"https://rec-letter.test/api/public/pdf-download/merge_EYwi90NqH3yKYQv2_1753773730","total_execution_time":15.75361680984497,"memory_peak":6291456,"step":"job_complete"} 
[2025-07-29 15:22:25] local.INFO: [DISPATCH] PDF壓縮任務已派發 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","queue_connection":"sync","execution_mode":"immediate (sync)"} 
[2025-07-29 15:22:25] local.INFO: [START] PDF壓縮任務已啟動 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730"} 
[2025-07-29 15:22:31] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-29 15:22:31] local.INFO: [REQUEST] 收到任務狀態查詢請求 {"query":{"task_id":"merge_EYwi90NqH3yKYQv2_1753773730"},"input":{"task_id":"merge_EYwi90NqH3yKYQv2_1753773730"},"ip":"127.0.0.1"} 
[2025-07-29 15:22:31] local.INFO: [RESPONSE] 回傳任務狀態 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","status":"ready","message":"任務完成，可以下載","ip":"127.0.0.1"} 
[2025-07-29 15:22:32] local.INFO: 公共PDF壓縮檔案下載 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","file_path":"pdf_packages/large_test_merge_EYwi90NqH3yKYQv2_1753773730.zip","client_ip":"127.0.0.1","user_agent":"GuzzleHttp/7"} 
[2025-07-29 15:23:07] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-29 15:23:07] local.INFO: [REQUEST] 收到任務狀態查詢請求 {"query":{"task_id":"merge_EYwi90NqH3yKYQv2_1753773730"},"input":{"task_id":"merge_EYwi90NqH3yKYQv2_1753773730"},"ip":"127.0.0.1"} 
[2025-07-29 15:23:07] local.INFO: [RESPONSE] 回傳任務狀態 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","status":"ready","message":"任務完成，可以下載","ip":"127.0.0.1"} 
[2025-07-29 15:23:07] local.INFO: 公共PDF壓縮檔案下載 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","file_path":"pdf_packages/large_test_merge_EYwi90NqH3yKYQv2_1753773730.zip","client_ip":"127.0.0.1","user_agent":"GuzzleHttp/7"} 
[2025-07-29 15:23:42] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-29 15:23:42] local.INFO: [REQUEST] 收到任務狀態查詢請求 {"query":{"task_id":"merge_EYwi90NqH3yKYQv2_1753773730"},"input":{"task_id":"merge_EYwi90NqH3yKYQv2_1753773730"},"ip":"127.0.0.1"} 
[2025-07-29 15:23:42] local.INFO: [RESPONSE] 回傳任務狀態 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","status":"ready","message":"任務完成，可以下載","ip":"127.0.0.1"} 
[2025-07-29 15:23:43] local.INFO: 公共PDF壓縮檔案下載 {"task_id":"merge_EYwi90NqH3yKYQv2_1753773730","file_path":"pdf_packages/large_test_merge_EYwi90NqH3yKYQv2_1753773730.zip","client_ip":"127.0.0.1","user_agent":"GuzzleHttp/7"} 
[2025-07-29 15:24:11] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-29 15:24:11] local.INFO: [TEST] 啟動測試模式 {"test_mode":"large_zip","test_file_count":"default","test_delay_seconds":"default","client_ip":"127.0.0.1"} 
[2025-07-29 15:24:11] local.INFO: [TEST] 生成測試數據 {"test_mode":"large_zip","file_count":1} 
[2025-07-29 15:24:11] local.INFO: [TASK] PDF壓縮任務執行 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","queue":"sync","test_mode":"large_zip"} 
[2025-07-29 15:24:11] local.INFO: [START] PDF壓縮Job開始執行 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","job_id":"","queue":"sync","attempts":1,"max_tries":3,"timeout":1800,"parameters":{"exam_id":"2","exam_year":"114","test_mode":"large_zip","requested_at":"2025-07-29T07:24:11.025995Z","client_ip":"127.0.0.1"},"memory_usage":4194304,"memory_peak":4194304,"timestamp":"2025-07-29T07:24:11.079446Z"} 
[2025-07-29 15:24:11] local.INFO: [PROCESS] 開始處理PDF壓縮任務 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","job_id":"","task_status":"processing","parameters":{"exam_id":"2","exam_year":"114","test_mode":"large_zip","requested_at":"2025-07-29T07:24:11.025995Z","client_ip":"127.0.0.1"},"test_mode":"large_zip","step":"initialization"} 
[2025-07-29 15:24:11] local.INFO: [STEP1] 獲取推薦函資料 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","step":"get_recommendations","test_mode":"large_zip","parameters":{"exam_id":"2","exam_year":"114","test_mode":"large_zip","requested_at":"2025-07-29T07:24:11.025995Z","client_ip":"127.0.0.1"}} 
[2025-07-29 15:24:11] local.INFO: [TEST] 生成測試推薦函數據 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","test_mode":"large_zip","file_count":10} 
[2025-07-29 15:24:11] local.INFO: [SUCCESS] 成功獲取推薦函 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","recommendation_count":10,"step":"get_recommendations_success"} 
[2025-07-29 15:24:11] local.INFO: [STEP2] 按考生分組 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","step":"group_by_applicant"} 
[2025-07-29 15:24:11] local.INFO: [SUCCESS] 考生分組完成 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","applicant_count":4,"step":"group_by_applicant_success"} 
[2025-07-29 15:24:11] local.INFO: [STEP3] 準備檔案列表 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","step":"prepare_files","applicant_count":4} 
[2025-07-29 15:24:11] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","applicant_id":"test_autono_1","recommendation_count":3,"progress":1,"total":4} 
[2025-07-29 15:24:11] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","recommendation_id":"test_rec_1","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_1.pdf","autono":"test_autono_1"} 
[2025-07-29 15:24:11] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","recommendation_id":"test_rec_2","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_2.pdf","autono":"test_autono_1"} 
[2025-07-29 15:24:11] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","recommendation_id":"test_rec_3","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_3.pdf","autono":"test_autono_1"} 
[2025-07-29 15:24:11] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","applicant_id":"test_autono_1","file_count":3} 
[2025-07-29 15:24:11] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","applicant_id":"test_autono_2","recommendation_count":3,"progress":2,"total":4} 
[2025-07-29 15:24:11] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","recommendation_id":"test_rec_4","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_4.pdf","autono":"test_autono_2"} 
[2025-07-29 15:24:11] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","recommendation_id":"test_rec_5","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_5.pdf","autono":"test_autono_2"} 
[2025-07-29 15:24:11] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","recommendation_id":"test_rec_6","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_6.pdf","autono":"test_autono_2"} 
[2025-07-29 15:24:11] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","applicant_id":"test_autono_2","file_count":3} 
[2025-07-29 15:24:11] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","applicant_id":"test_autono_3","recommendation_count":3,"progress":3,"total":4} 
[2025-07-29 15:24:11] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","recommendation_id":"test_rec_7","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_7.pdf","autono":"test_autono_3"} 
[2025-07-29 15:24:11] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","recommendation_id":"test_rec_8","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_8.pdf","autono":"test_autono_3"} 
[2025-07-29 15:24:11] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","recommendation_id":"test_rec_9","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_9.pdf","autono":"test_autono_3"} 
[2025-07-29 15:24:11] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","applicant_id":"test_autono_3","file_count":3} 
[2025-07-29 15:24:11] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","applicant_id":"test_autono_4","recommendation_count":1,"progress":4,"total":4} 
[2025-07-29 15:24:11] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","recommendation_id":"test_rec_10","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_10.pdf","autono":"test_autono_4"} 
[2025-07-29 15:24:11] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","applicant_id":"test_autono_4","file_count":1} 
[2025-07-29 15:24:11] local.INFO: [STATS] 檔案準備統計 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","total_files":10,"processed_applicants":4,"step":"prepare_files_complete"} 
[2025-07-29 15:24:11] local.INFO: [STEP4] 開始建立ZIP檔案 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","step":"create_zip","file_count":10,"test_mode":"large_zip","memory_usage":4194304} 
[2025-07-29 15:24:11] local.INFO: [LARGE_ZIP] 開始創建3GB大型ZIP檔案 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","target_size":"3GB"} 
[2025-07-29 15:24:11] local.INFO: [LARGE_ZIP] 開始寫入大型檔案 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","target_size_bytes":3221225472,"chunk_size_bytes":1048576} 
[2025-07-29 15:24:12] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","progress":"10%","written_bytes":322961408,"written_mb":308.0} 
[2025-07-29 15:24:14] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","progress":"20%","written_bytes":644874240,"written_mb":615.0} 
[2025-07-29 15:24:15] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","progress":"30%","written_bytes":966787072,"written_mb":922.0} 
[2025-07-29 15:24:16] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","progress":"40%","written_bytes":1288699904,"written_mb":1229.0} 
[2025-07-29 15:24:17] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","progress":"50%","written_bytes":1610612736,"written_mb":1536.0} 
[2025-07-29 15:24:19] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","progress":"60%","written_bytes":1933574144,"written_mb":1844.0} 
[2025-07-29 15:24:21] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","progress":"70%","written_bytes":2255486976,"written_mb":2151.0} 
[2025-07-29 15:24:22] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","progress":"80%","written_bytes":2577399808,"written_mb":2458.0} 
[2025-07-29 15:24:23] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","progress":"90%","written_bytes":2899312640,"written_mb":2765.0} 
[2025-07-29 15:24:25] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","progress":"100%","written_bytes":3221225472,"written_mb":3072.0} 
[2025-07-29 15:24:25] local.INFO: [LARGE_ZIP] 大型ZIP檔案創建完成 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","file_path":"pdf_packages/large_test_merge_BgZgSR93WEANhz3f_1753773851.zip","final_size_bytes":3221225472,"final_size_gb":3.0} 
[2025-07-29 15:24:25] local.INFO: [SUCCESS] ZIP檔案建立完成 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","zip_file_path":"pdf_packages/large_test_merge_BgZgSR93WEANhz3f_1753773851.zip","step":"create_zip_success"} 
[2025-07-29 15:24:25] local.INFO: [STEP5] 生成下載URL {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","step":"generate_download_url"} 
[2025-07-29 15:24:25] local.INFO: [SUCCESS] 下載URL生成完成 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","download_url":"https://rec-letter.test/api/public/pdf-download/merge_BgZgSR93WEANhz3f_1753773851","step":"generate_download_url_success"} 
[2025-07-29 15:24:25] local.INFO: [STEP6] 標記任務完成 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","step":"mark_as_ready"} 
[2025-07-29 15:24:25] local.INFO: [COMPLETE] PDF壓縮任務完成 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","job_id":"","compressed_files":10,"zip_file":"pdf_packages/large_test_merge_BgZgSR93WEANhz3f_1753773851.zip","download_url":"https://rec-letter.test/api/public/pdf-download/merge_BgZgSR93WEANhz3f_1753773851","total_execution_time":14.778970003128052,"memory_peak":6291456,"step":"job_complete"} 
[2025-07-29 15:24:25] local.INFO: [DISPATCH] PDF壓縮任務已派發 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","queue_connection":"sync","execution_mode":"immediate (sync)"} 
[2025-07-29 15:24:25] local.INFO: [START] PDF壓縮任務已啟動 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851"} 
[2025-07-29 15:24:31] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-29 15:24:31] local.INFO: [REQUEST] 收到任務狀態查詢請求 {"query":{"task_id":"merge_BgZgSR93WEANhz3f_1753773851"},"input":{"task_id":"merge_BgZgSR93WEANhz3f_1753773851"},"ip":"127.0.0.1"} 
[2025-07-29 15:24:31] local.INFO: [RESPONSE] 回傳任務狀態 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","status":"ready","message":"任務完成，可以下載","ip":"127.0.0.1"} 
[2025-07-29 15:24:31] local.INFO: 公共PDF壓縮檔案下載 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","file_path":"pdf_packages/large_test_merge_BgZgSR93WEANhz3f_1753773851.zip","client_ip":"127.0.0.1","user_agent":"GuzzleHttp/7"} 
[2025-07-29 15:25:01] local.ERROR: Maximum execution time of 30 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 30 seconds exceeded at C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\http-foundation\\BinaryFileResponse.php:338)
[stacktrace]
#0 {main}
"} 
[2025-07-29 15:25:02] local.ERROR: Cannot modify header information - headers already sent by (output started at C:\Users\<USER>\Desktop\rec-letter\vendor\symfony\http-foundation\BinaryFileResponse.php:338) {"exception":"[object] (ErrorException(code: 0): Cannot modify header information - headers already sent by (output started at C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\http-foundation\\BinaryFileResponse.php:338) at C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\http-foundation\\Response.php:322)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Cannot modify h...', 'C:\\\\Users\\\\<USER>\\\\D...', 322)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Cannot modify h...', 'C:\\\\Users\\\\<USER>\\\\D...', 322)
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\http-foundation\\Response.php(322): header('HTTP/1.1 500 In...', true, 500)
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\http-foundation\\Response.php(401): Symfony\\Component\\HttpFoundation\\Response->sendHeaders()
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(219): Symfony\\Component\\HttpFoundation\\Response->send()
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(196): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->renderHttpResponse(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(232): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#8 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}()
#9 {main}
"} 
[2025-07-29 15:25:08] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-29 15:25:08] local.INFO: [REQUEST] 收到任務狀態查詢請求 {"query":{"task_id":"merge_BgZgSR93WEANhz3f_1753773851"},"input":{"task_id":"merge_BgZgSR93WEANhz3f_1753773851"},"ip":"127.0.0.1"} 
[2025-07-29 15:25:08] local.INFO: [RESPONSE] 回傳任務狀態 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","status":"ready","message":"任務完成，可以下載","ip":"127.0.0.1"} 
[2025-07-29 15:25:09] local.INFO: 公共PDF壓縮檔案下載 {"task_id":"merge_BgZgSR93WEANhz3f_1753773851","file_path":"pdf_packages/large_test_merge_BgZgSR93WEANhz3f_1753773851.zip","client_ip":"127.0.0.1","user_agent":"GuzzleHttp/7"} 
[2025-07-29 15:25:39] local.ERROR: Maximum execution time of 30 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 30 seconds exceeded at C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\http-foundation\\BinaryFileResponse.php:334)
[stacktrace]
#0 {main}
"} 
[2025-07-29 15:25:39] local.ERROR: Cannot modify header information - headers already sent by (output started at C:\Users\<USER>\Desktop\rec-letter\vendor\symfony\http-foundation\BinaryFileResponse.php:338) {"exception":"[object] (ErrorException(code: 0): Cannot modify header information - headers already sent by (output started at C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\http-foundation\\BinaryFileResponse.php:338) at C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\http-foundation\\Response.php:322)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Cannot modify h...', 'C:\\\\Users\\\\<USER>\\\\D...', 322)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Cannot modify h...', 'C:\\\\Users\\\\<USER>\\\\D...', 322)
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\http-foundation\\Response.php(322): header('HTTP/1.1 500 In...', true, 500)
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\http-foundation\\Response.php(401): Symfony\\Component\\HttpFoundation\\Response->sendHeaders()
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(219): Symfony\\Component\\HttpFoundation\\Response->send()
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(196): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->renderHttpResponse(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(232): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#8 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}()
#9 {main}
"} 
[2025-07-29 15:29:39] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-29 15:29:39] local.INFO: [TEST] 啟動測試模式 {"test_mode":"large_zip","test_file_count":"default","test_delay_seconds":"default","client_ip":"127.0.0.1"} 
[2025-07-29 15:29:39] local.INFO: [TEST] 生成測試數據 {"test_mode":"large_zip","file_count":1} 
[2025-07-29 15:29:39] local.INFO: [TASK] PDF壓縮任務執行 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","queue":"sync","test_mode":"large_zip"} 
[2025-07-29 15:29:39] local.INFO: [START] PDF壓縮Job開始執行 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","job_id":"","queue":"sync","attempts":1,"max_tries":3,"timeout":1800,"parameters":{"exam_id":"2","exam_year":"114","test_mode":"large_zip","requested_at":"2025-07-29T07:29:39.603287Z","client_ip":"127.0.0.1"},"memory_usage":4194304,"memory_peak":4194304,"timestamp":"2025-07-29T07:29:39.729578Z"} 
[2025-07-29 15:29:39] local.INFO: [PROCESS] 開始處理PDF壓縮任務 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","job_id":"","task_status":"processing","parameters":{"exam_id":"2","exam_year":"114","test_mode":"large_zip","requested_at":"2025-07-29T07:29:39.603287Z","client_ip":"127.0.0.1"},"test_mode":"large_zip","step":"initialization"} 
[2025-07-29 15:29:39] local.INFO: [STEP1] 獲取推薦函資料 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","step":"get_recommendations","test_mode":"large_zip","parameters":{"exam_id":"2","exam_year":"114","test_mode":"large_zip","requested_at":"2025-07-29T07:29:39.603287Z","client_ip":"127.0.0.1"}} 
[2025-07-29 15:29:39] local.INFO: [TEST] 生成測試推薦函數據 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","test_mode":"large_zip","file_count":10} 
[2025-07-29 15:29:39] local.INFO: [SUCCESS] 成功獲取推薦函 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","recommendation_count":10,"step":"get_recommendations_success"} 
[2025-07-29 15:29:39] local.INFO: [STEP2] 按考生分組 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","step":"group_by_applicant"} 
[2025-07-29 15:29:39] local.INFO: [SUCCESS] 考生分組完成 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","applicant_count":4,"step":"group_by_applicant_success"} 
[2025-07-29 15:29:39] local.INFO: [STEP3] 準備檔案列表 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","step":"prepare_files","applicant_count":4} 
[2025-07-29 15:29:39] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","applicant_id":"test_autono_1","recommendation_count":3,"progress":1,"total":4} 
[2025-07-29 15:29:39] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","recommendation_id":"test_rec_1","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_1.pdf","autono":"test_autono_1"} 
[2025-07-29 15:29:39] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","recommendation_id":"test_rec_2","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_2.pdf","autono":"test_autono_1"} 
[2025-07-29 15:29:39] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","recommendation_id":"test_rec_3","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_3.pdf","autono":"test_autono_1"} 
[2025-07-29 15:29:39] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","applicant_id":"test_autono_1","file_count":3} 
[2025-07-29 15:29:39] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","applicant_id":"test_autono_2","recommendation_count":3,"progress":2,"total":4} 
[2025-07-29 15:29:39] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","recommendation_id":"test_rec_4","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_4.pdf","autono":"test_autono_2"} 
[2025-07-29 15:29:39] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","recommendation_id":"test_rec_5","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_5.pdf","autono":"test_autono_2"} 
[2025-07-29 15:29:39] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","recommendation_id":"test_rec_6","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_6.pdf","autono":"test_autono_2"} 
[2025-07-29 15:29:39] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","applicant_id":"test_autono_2","file_count":3} 
[2025-07-29 15:29:39] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","applicant_id":"test_autono_3","recommendation_count":3,"progress":3,"total":4} 
[2025-07-29 15:29:39] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","recommendation_id":"test_rec_7","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_7.pdf","autono":"test_autono_3"} 
[2025-07-29 15:29:39] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","recommendation_id":"test_rec_8","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_8.pdf","autono":"test_autono_3"} 
[2025-07-29 15:29:39] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","recommendation_id":"test_rec_9","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_9.pdf","autono":"test_autono_3"} 
[2025-07-29 15:29:39] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","applicant_id":"test_autono_3","file_count":3} 
[2025-07-29 15:29:39] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","applicant_id":"test_autono_4","recommendation_count":1,"progress":4,"total":4} 
[2025-07-29 15:29:39] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","recommendation_id":"test_rec_10","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_10.pdf","autono":"test_autono_4"} 
[2025-07-29 15:29:39] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","applicant_id":"test_autono_4","file_count":1} 
[2025-07-29 15:29:39] local.INFO: [STATS] 檔案準備統計 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","total_files":10,"processed_applicants":4,"step":"prepare_files_complete"} 
[2025-07-29 15:29:39] local.INFO: [STEP4] 開始建立ZIP檔案 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","step":"create_zip","file_count":10,"test_mode":"large_zip","memory_usage":4194304} 
[2025-07-29 15:29:39] local.INFO: [LARGE_ZIP] 開始創建3GB大型ZIP檔案 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","target_size":"3GB"} 
[2025-07-29 15:29:39] local.INFO: [LARGE_ZIP] 開始寫入大型檔案 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","target_size_bytes":3221225472,"chunk_size_bytes":1048576} 
[2025-07-29 15:29:40] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","progress":"10%","written_bytes":322961408,"written_mb":308.0} 
[2025-07-29 15:29:44] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","progress":"20%","written_bytes":644874240,"written_mb":615.0} 
[2025-07-29 15:29:45] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","progress":"30%","written_bytes":966787072,"written_mb":922.0} 
[2025-07-29 15:29:48] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","progress":"40%","written_bytes":1288699904,"written_mb":1229.0} 
[2025-07-29 15:29:49] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","progress":"50%","written_bytes":1610612736,"written_mb":1536.0} 
[2025-07-29 15:29:50] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","progress":"60%","written_bytes":1933574144,"written_mb":1844.0} 
[2025-07-29 15:29:51] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","progress":"70%","written_bytes":2255486976,"written_mb":2151.0} 
[2025-07-29 15:29:53] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","progress":"80%","written_bytes":2577399808,"written_mb":2458.0} 
[2025-07-29 15:29:54] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","progress":"90%","written_bytes":2899312640,"written_mb":2765.0} 
[2025-07-29 15:29:55] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","progress":"100%","written_bytes":3221225472,"written_mb":3072.0} 
[2025-07-29 15:29:55] local.INFO: [LARGE_ZIP] 大型ZIP檔案創建完成 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","file_path":"pdf_packages/large_test_merge_x0bHif6EqcpAT6x7_1753774179.zip","final_size_bytes":3221225472,"final_size_gb":3.0} 
[2025-07-29 15:29:55] local.INFO: [SUCCESS] ZIP檔案建立完成 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","zip_file_path":"pdf_packages/large_test_merge_x0bHif6EqcpAT6x7_1753774179.zip","step":"create_zip_success"} 
[2025-07-29 15:29:55] local.INFO: [STEP5] 生成下載URL {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","step":"generate_download_url"} 
[2025-07-29 15:29:55] local.INFO: [SUCCESS] 下載URL生成完成 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","download_url":"https://rec-letter.test/api/public/pdf-download/merge_x0bHif6EqcpAT6x7_1753774179","step":"generate_download_url_success"} 
[2025-07-29 15:29:55] local.INFO: [STEP6] 標記任務完成 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","step":"mark_as_ready"} 
[2025-07-29 15:29:55] local.INFO: [COMPLETE] PDF壓縮任務完成 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","job_id":"","compressed_files":10,"zip_file":"pdf_packages/large_test_merge_x0bHif6EqcpAT6x7_1753774179.zip","download_url":"https://rec-letter.test/api/public/pdf-download/merge_x0bHif6EqcpAT6x7_1753774179","total_execution_time":16.16173481941223,"memory_peak":6291456,"step":"job_complete"} 
[2025-07-29 15:29:55] local.INFO: [DISPATCH] PDF壓縮任務已派發 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","queue_connection":"sync","execution_mode":"immediate (sync)"} 
[2025-07-29 15:29:55] local.INFO: [START] PDF壓縮任務已啟動 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179"} 
[2025-07-29 15:30:01] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-29 15:30:01] local.INFO: [REQUEST] 收到任務狀態查詢請求 {"query":{"task_id":"merge_x0bHif6EqcpAT6x7_1753774179"},"input":{"task_id":"merge_x0bHif6EqcpAT6x7_1753774179"},"ip":"127.0.0.1"} 
[2025-07-29 15:30:01] local.INFO: [RESPONSE] 回傳任務狀態 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","status":"ready","message":"任務完成，可以下載","ip":"127.0.0.1"} 
[2025-07-29 15:30:01] local.INFO: 公共PDF壓縮檔案下載 {"task_id":"merge_x0bHif6EqcpAT6x7_1753774179","file_path":"pdf_packages/large_test_merge_x0bHif6EqcpAT6x7_1753774179.zip","client_ip":"127.0.0.1","user_agent":"GuzzleHttp/7"} 
[2025-07-29 15:30:31] local.ERROR: Maximum execution time of 30 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 30 seconds exceeded at C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Controllers\\Api\\PdfMergeApiController.php:347)
[stacktrace]
#0 {main}
"} 
[2025-07-29 15:30:32] local.ERROR: Cannot modify header information - headers already sent by (output started at C:\Users\<USER>\Desktop\rec-letter\app\Http\Controllers\Api\PdfMergeApiController.php:348) {"exception":"[object] (ErrorException(code: 0): Cannot modify header information - headers already sent by (output started at C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Controllers\\Api\\PdfMergeApiController.php:348) at C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\http-foundation\\Response.php:322)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Cannot modify h...', 'C:\\\\Users\\\\<USER>\\\\D...', 322)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Cannot modify h...', 'C:\\\\Users\\\\<USER>\\\\D...', 322)
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\http-foundation\\Response.php(322): header('HTTP/1.1 500 In...', true, 500)
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\http-foundation\\Response.php(401): Symfony\\Component\\HttpFoundation\\Response->sendHeaders()
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(219): Symfony\\Component\\HttpFoundation\\Response->send()
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(196): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->renderHttpResponse(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(232): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#8 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}()
#9 {main}
"} 
[2025-07-29 15:38:03] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-29 15:38:03] local.INFO: [TEST] 啟動測試模式 {"test_mode":"large_zip","test_file_count":"default","test_delay_seconds":"default","client_ip":"127.0.0.1"} 
[2025-07-29 15:38:03] local.INFO: [TEST] 生成測試數據 {"test_mode":"large_zip","file_count":1} 
[2025-07-29 15:38:03] local.INFO: [TASK] PDF壓縮任務執行 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","queue":"sync","test_mode":"large_zip"} 
[2025-07-29 15:38:03] local.INFO: [START] PDF壓縮Job開始執行 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","job_id":"","queue":"sync","attempts":1,"max_tries":3,"timeout":1800,"parameters":{"exam_id":"2","exam_year":"114","test_mode":"large_zip","requested_at":"2025-07-29T07:38:03.593511Z","client_ip":"127.0.0.1"},"memory_usage":4194304,"memory_peak":4194304,"timestamp":"2025-07-29T07:38:03.650799Z"} 
[2025-07-29 15:38:03] local.INFO: [PROCESS] 開始處理PDF壓縮任務 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","job_id":"","task_status":"processing","parameters":{"exam_id":"2","exam_year":"114","test_mode":"large_zip","requested_at":"2025-07-29T07:38:03.593511Z","client_ip":"127.0.0.1"},"test_mode":"large_zip","step":"initialization"} 
[2025-07-29 15:38:03] local.INFO: [STEP1] 獲取推薦函資料 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","step":"get_recommendations","test_mode":"large_zip","parameters":{"exam_id":"2","exam_year":"114","test_mode":"large_zip","requested_at":"2025-07-29T07:38:03.593511Z","client_ip":"127.0.0.1"}} 
[2025-07-29 15:38:03] local.INFO: [TEST] 生成測試推薦函數據 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","test_mode":"large_zip","file_count":10} 
[2025-07-29 15:38:03] local.INFO: [SUCCESS] 成功獲取推薦函 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","recommendation_count":10,"step":"get_recommendations_success"} 
[2025-07-29 15:38:03] local.INFO: [STEP2] 按考生分組 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","step":"group_by_applicant"} 
[2025-07-29 15:38:03] local.INFO: [SUCCESS] 考生分組完成 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","applicant_count":4,"step":"group_by_applicant_success"} 
[2025-07-29 15:38:03] local.INFO: [STEP3] 準備檔案列表 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","step":"prepare_files","applicant_count":4} 
[2025-07-29 15:38:03] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","applicant_id":"test_autono_1","recommendation_count":3,"progress":1,"total":4} 
[2025-07-29 15:38:03] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","recommendation_id":"test_rec_1","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_1.pdf","autono":"test_autono_1"} 
[2025-07-29 15:38:03] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","recommendation_id":"test_rec_2","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_2.pdf","autono":"test_autono_1"} 
[2025-07-29 15:38:03] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","recommendation_id":"test_rec_3","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_3.pdf","autono":"test_autono_1"} 
[2025-07-29 15:38:03] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","applicant_id":"test_autono_1","file_count":3} 
[2025-07-29 15:38:03] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","applicant_id":"test_autono_2","recommendation_count":3,"progress":2,"total":4} 
[2025-07-29 15:38:03] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","recommendation_id":"test_rec_4","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_4.pdf","autono":"test_autono_2"} 
[2025-07-29 15:38:03] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","recommendation_id":"test_rec_5","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_5.pdf","autono":"test_autono_2"} 
[2025-07-29 15:38:03] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","recommendation_id":"test_rec_6","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_6.pdf","autono":"test_autono_2"} 
[2025-07-29 15:38:03] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","applicant_id":"test_autono_2","file_count":3} 
[2025-07-29 15:38:03] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","applicant_id":"test_autono_3","recommendation_count":3,"progress":3,"total":4} 
[2025-07-29 15:38:03] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","recommendation_id":"test_rec_7","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_7.pdf","autono":"test_autono_3"} 
[2025-07-29 15:38:03] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","recommendation_id":"test_rec_8","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_8.pdf","autono":"test_autono_3"} 
[2025-07-29 15:38:03] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","recommendation_id":"test_rec_9","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_9.pdf","autono":"test_autono_3"} 
[2025-07-29 15:38:03] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","applicant_id":"test_autono_3","file_count":3} 
[2025-07-29 15:38:03] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","applicant_id":"test_autono_4","recommendation_count":1,"progress":4,"total":4} 
[2025-07-29 15:38:03] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","recommendation_id":"test_rec_10","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_10.pdf","autono":"test_autono_4"} 
[2025-07-29 15:38:03] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","applicant_id":"test_autono_4","file_count":1} 
[2025-07-29 15:38:03] local.INFO: [STATS] 檔案準備統計 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","total_files":10,"processed_applicants":4,"step":"prepare_files_complete"} 
[2025-07-29 15:38:03] local.INFO: [STEP4] 開始建立ZIP檔案 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","step":"create_zip","file_count":10,"test_mode":"large_zip","memory_usage":4194304} 
[2025-07-29 15:38:03] local.INFO: [LARGE_ZIP] 開始創建3GB大型ZIP檔案 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","target_size":"3GB"} 
[2025-07-29 15:38:03] local.INFO: [LARGE_ZIP] 開始寫入大型檔案 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","target_size_bytes":3221225472,"chunk_size_bytes":1048576} 
[2025-07-29 15:38:04] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","progress":"10%","written_bytes":322961408,"written_mb":308.0} 
[2025-07-29 15:38:05] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","progress":"20%","written_bytes":644874240,"written_mb":615.0} 
[2025-07-29 15:38:08] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","progress":"30%","written_bytes":966787072,"written_mb":922.0} 
[2025-07-29 15:38:09] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","progress":"40%","written_bytes":1288699904,"written_mb":1229.0} 
[2025-07-29 15:38:10] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","progress":"50%","written_bytes":1610612736,"written_mb":1536.0} 
[2025-07-29 15:38:12] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","progress":"60%","written_bytes":1933574144,"written_mb":1844.0} 
[2025-07-29 15:38:13] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","progress":"70%","written_bytes":2255486976,"written_mb":2151.0} 
[2025-07-29 15:38:15] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","progress":"80%","written_bytes":2577399808,"written_mb":2458.0} 
[2025-07-29 15:38:16] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","progress":"90%","written_bytes":2899312640,"written_mb":2765.0} 
[2025-07-29 15:38:17] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","progress":"100%","written_bytes":3221225472,"written_mb":3072.0} 
[2025-07-29 15:38:17] local.INFO: [LARGE_ZIP] 大型ZIP檔案創建完成 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","file_path":"pdf_packages/large_test_merge_P8dTs5c4koM6tQ8N_1753774683.zip","final_size_bytes":3221225472,"final_size_gb":3.0} 
[2025-07-29 15:38:17] local.INFO: [SUCCESS] ZIP檔案建立完成 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","zip_file_path":"pdf_packages/large_test_merge_P8dTs5c4koM6tQ8N_1753774683.zip","step":"create_zip_success"} 
[2025-07-29 15:38:17] local.INFO: [STEP5] 生成下載URL {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","step":"generate_download_url"} 
[2025-07-29 15:38:17] local.INFO: [SUCCESS] 下載URL生成完成 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","download_url":"https://rec-letter.test/api/public/pdf-download/merge_P8dTs5c4koM6tQ8N_1753774683","step":"generate_download_url_success"} 
[2025-07-29 15:38:17] local.INFO: [STEP6] 標記任務完成 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","step":"mark_as_ready"} 
[2025-07-29 15:38:17] local.INFO: [COMPLETE] PDF壓縮任務完成 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","job_id":"","compressed_files":10,"zip_file":"pdf_packages/large_test_merge_P8dTs5c4koM6tQ8N_1753774683.zip","download_url":"https://rec-letter.test/api/public/pdf-download/merge_P8dTs5c4koM6tQ8N_1753774683","total_execution_time":14.391057968139648,"memory_peak":6291456,"step":"job_complete"} 
[2025-07-29 15:38:17] local.INFO: [DISPATCH] PDF壓縮任務已派發 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","queue_connection":"sync","execution_mode":"immediate (sync)"} 
[2025-07-29 15:38:17] local.INFO: [START] PDF壓縮任務已啟動 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683"} 
[2025-07-29 15:38:23] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-29 15:38:23] local.INFO: [REQUEST] 收到任務狀態查詢請求 {"query":{"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683"},"input":{"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683"},"ip":"127.0.0.1"} 
[2025-07-29 15:38:23] local.INFO: [RESPONSE] 回傳任務狀態 {"task_id":"merge_P8dTs5c4koM6tQ8N_1753774683","status":"ready","message":"任務完成，可以下載","ip":"127.0.0.1"} 
[2025-07-29 15:39:21] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-29 15:39:21] local.INFO: [TEST] 啟動測試模式 {"test_mode":"large_zip","test_file_count":"default","test_delay_seconds":"default","client_ip":"127.0.0.1"} 
[2025-07-29 15:39:21] local.INFO: [TEST] 生成測試數據 {"test_mode":"large_zip","file_count":1} 
[2025-07-29 15:39:21] local.INFO: [TASK] PDF壓縮任務執行 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","queue":"sync","test_mode":"large_zip"} 
[2025-07-29 15:39:21] local.INFO: [START] PDF壓縮Job開始執行 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","job_id":"","queue":"sync","attempts":1,"max_tries":3,"timeout":1800,"parameters":{"exam_id":"2","exam_year":"114","test_mode":"large_zip","requested_at":"2025-07-29T07:39:21.181568Z","client_ip":"127.0.0.1"},"memory_usage":4194304,"memory_peak":4194304,"timestamp":"2025-07-29T07:39:21.217314Z"} 
[2025-07-29 15:39:21] local.INFO: [PROCESS] 開始處理PDF壓縮任務 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","job_id":"","task_status":"processing","parameters":{"exam_id":"2","exam_year":"114","test_mode":"large_zip","requested_at":"2025-07-29T07:39:21.181568Z","client_ip":"127.0.0.1"},"test_mode":"large_zip","step":"initialization"} 
[2025-07-29 15:39:21] local.INFO: [STEP1] 獲取推薦函資料 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","step":"get_recommendations","test_mode":"large_zip","parameters":{"exam_id":"2","exam_year":"114","test_mode":"large_zip","requested_at":"2025-07-29T07:39:21.181568Z","client_ip":"127.0.0.1"}} 
[2025-07-29 15:39:21] local.INFO: [TEST] 生成測試推薦函數據 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","test_mode":"large_zip","file_count":10} 
[2025-07-29 15:39:21] local.INFO: [SUCCESS] 成功獲取推薦函 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","recommendation_count":10,"step":"get_recommendations_success"} 
[2025-07-29 15:39:21] local.INFO: [STEP2] 按考生分組 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","step":"group_by_applicant"} 
[2025-07-29 15:39:21] local.INFO: [SUCCESS] 考生分組完成 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","applicant_count":4,"step":"group_by_applicant_success"} 
[2025-07-29 15:39:21] local.INFO: [STEP3] 準備檔案列表 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","step":"prepare_files","applicant_count":4} 
[2025-07-29 15:39:21] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","applicant_id":"test_autono_1","recommendation_count":3,"progress":1,"total":4} 
[2025-07-29 15:39:21] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","recommendation_id":"test_rec_1","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_1.pdf","autono":"test_autono_1"} 
[2025-07-29 15:39:21] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","recommendation_id":"test_rec_2","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_2.pdf","autono":"test_autono_1"} 
[2025-07-29 15:39:21] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","recommendation_id":"test_rec_3","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_3.pdf","autono":"test_autono_1"} 
[2025-07-29 15:39:21] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","applicant_id":"test_autono_1","file_count":3} 
[2025-07-29 15:39:21] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","applicant_id":"test_autono_2","recommendation_count":3,"progress":2,"total":4} 
[2025-07-29 15:39:21] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","recommendation_id":"test_rec_4","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_4.pdf","autono":"test_autono_2"} 
[2025-07-29 15:39:21] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","recommendation_id":"test_rec_5","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_5.pdf","autono":"test_autono_2"} 
[2025-07-29 15:39:21] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","recommendation_id":"test_rec_6","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_6.pdf","autono":"test_autono_2"} 
[2025-07-29 15:39:21] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","applicant_id":"test_autono_2","file_count":3} 
[2025-07-29 15:39:21] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","applicant_id":"test_autono_3","recommendation_count":3,"progress":3,"total":4} 
[2025-07-29 15:39:21] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","recommendation_id":"test_rec_7","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_7.pdf","autono":"test_autono_3"} 
[2025-07-29 15:39:21] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","recommendation_id":"test_rec_8","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_8.pdf","autono":"test_autono_3"} 
[2025-07-29 15:39:21] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","recommendation_id":"test_rec_9","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_9.pdf","autono":"test_autono_3"} 
[2025-07-29 15:39:21] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","applicant_id":"test_autono_3","file_count":3} 
[2025-07-29 15:39:21] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","applicant_id":"test_autono_4","recommendation_count":1,"progress":4,"total":4} 
[2025-07-29 15:39:21] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","recommendation_id":"test_rec_10","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_10.pdf","autono":"test_autono_4"} 
[2025-07-29 15:39:21] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","applicant_id":"test_autono_4","file_count":1} 
[2025-07-29 15:39:21] local.INFO: [STATS] 檔案準備統計 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","total_files":10,"processed_applicants":4,"step":"prepare_files_complete"} 
[2025-07-29 15:39:21] local.INFO: [STEP4] 開始建立ZIP檔案 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","step":"create_zip","file_count":10,"test_mode":"large_zip","memory_usage":4194304} 
[2025-07-29 15:39:21] local.INFO: [LARGE_ZIP] 開始創建3GB大型ZIP檔案 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","target_size":"3GB"} 
[2025-07-29 15:39:21] local.INFO: [LARGE_ZIP] 開始寫入大型檔案 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","target_size_bytes":3221225472,"chunk_size_bytes":1048576} 
[2025-07-29 15:39:21] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","progress":"10%","written_bytes":322961408,"written_mb":308.0} 
[2025-07-29 15:39:22] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","progress":"20%","written_bytes":644874240,"written_mb":615.0} 
[2025-07-29 15:39:24] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","progress":"30%","written_bytes":966787072,"written_mb":922.0} 
[2025-07-29 15:39:25] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","progress":"40%","written_bytes":1288699904,"written_mb":1229.0} 
[2025-07-29 15:39:26] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","progress":"50%","written_bytes":1610612736,"written_mb":1536.0} 
[2025-07-29 15:39:28] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","progress":"60%","written_bytes":1933574144,"written_mb":1844.0} 
[2025-07-29 15:39:29] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","progress":"70%","written_bytes":2255486976,"written_mb":2151.0} 
[2025-07-29 15:39:31] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","progress":"80%","written_bytes":2577399808,"written_mb":2458.0} 
[2025-07-29 15:39:32] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","progress":"90%","written_bytes":2899312640,"written_mb":2765.0} 
[2025-07-29 15:39:33] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","progress":"100%","written_bytes":3221225472,"written_mb":3072.0} 
[2025-07-29 15:39:33] local.INFO: [LARGE_ZIP] 大型ZIP檔案創建完成 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","file_path":"pdf_packages/large_test_merge_4QjuGTW2Zq5SRNVP_1753774761.zip","final_size_bytes":3221225472,"final_size_gb":3.0} 
[2025-07-29 15:39:33] local.INFO: [SUCCESS] ZIP檔案建立完成 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","zip_file_path":"pdf_packages/large_test_merge_4QjuGTW2Zq5SRNVP_1753774761.zip","step":"create_zip_success"} 
[2025-07-29 15:39:33] local.INFO: [STEP5] 生成下載URL {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","step":"generate_download_url"} 
[2025-07-29 15:39:33] local.INFO: [SUCCESS] 下載URL生成完成 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","download_url":"https://rec-letter.test/api/public/pdf-download/merge_4QjuGTW2Zq5SRNVP_1753774761","step":"generate_download_url_success"} 
[2025-07-29 15:39:33] local.INFO: [STEP6] 標記任務完成 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","step":"mark_as_ready"} 
[2025-07-29 15:39:33] local.INFO: [COMPLETE] PDF壓縮任務完成 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","job_id":"","compressed_files":10,"zip_file":"pdf_packages/large_test_merge_4QjuGTW2Zq5SRNVP_1753774761.zip","download_url":"https://rec-letter.test/api/public/pdf-download/merge_4QjuGTW2Zq5SRNVP_1753774761","total_execution_time":12.581019878387451,"memory_peak":6291456,"step":"job_complete"} 
[2025-07-29 15:39:33] local.INFO: [DISPATCH] PDF壓縮任務已派發 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","queue_connection":"sync","execution_mode":"immediate (sync)"} 
[2025-07-29 15:39:33] local.INFO: [START] PDF壓縮任務已啟動 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761"} 
[2025-07-29 15:39:39] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-29 15:39:39] local.INFO: [REQUEST] 收到任務狀態查詢請求 {"query":{"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761"},"input":{"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761"},"ip":"127.0.0.1"} 
[2025-07-29 15:39:39] local.INFO: [RESPONSE] 回傳任務狀態 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","status":"ready","message":"任務完成，可以下載","ip":"127.0.0.1"} 
[2025-07-29 15:39:39] local.INFO: 公共PDF壓縮檔案下載 {"task_id":"merge_4QjuGTW2Zq5SRNVP_1753774761","file_path":"pdf_packages/large_test_merge_4QjuGTW2Zq5SRNVP_1753774761.zip","client_ip":"127.0.0.1","user_agent":"curl/8.13.0"} 
[2025-07-29 15:40:09] local.ERROR: Maximum execution time of 30 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 30 seconds exceeded at C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Controllers\\Api\\PdfMergeApiController.php:347)
[stacktrace]
#0 {main}
"} 
[2025-07-29 15:40:10] local.ERROR: Cannot modify header information - headers already sent by (output started at C:\Users\<USER>\Desktop\rec-letter\app\Http\Controllers\Api\PdfMergeApiController.php:348) {"exception":"[object] (ErrorException(code: 0): Cannot modify header information - headers already sent by (output started at C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Controllers\\Api\\PdfMergeApiController.php:348) at C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\http-foundation\\Response.php:322)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Cannot modify h...', 'C:\\\\Users\\\\<USER>\\\\D...', 322)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Cannot modify h...', 'C:\\\\Users\\\\<USER>\\\\D...', 322)
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\http-foundation\\Response.php(322): header('HTTP/1.1 500 In...', true, 500)
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\http-foundation\\Response.php(401): Symfony\\Component\\HttpFoundation\\Response->sendHeaders()
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(219): Symfony\\Component\\HttpFoundation\\Response->send()
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(196): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->renderHttpResponse(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(232): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#8 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}()
#9 {main}
"} 
[2025-07-29 15:44:38] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-29 15:44:38] local.INFO: [TEST] 啟動測試模式 {"test_mode":"large_zip","test_file_count":"default","test_delay_seconds":"default","client_ip":"127.0.0.1"} 
[2025-07-29 15:44:38] local.INFO: [TEST] 生成測試數據 {"test_mode":"large_zip","file_count":1} 
[2025-07-29 15:44:38] local.INFO: [TASK] PDF壓縮任務執行 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","queue":"sync","test_mode":"large_zip"} 
[2025-07-29 15:44:38] local.INFO: [START] PDF壓縮Job開始執行 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","job_id":"","queue":"sync","attempts":1,"max_tries":3,"timeout":1800,"parameters":{"exam_id":"2","exam_year":"114","test_mode":"large_zip","requested_at":"2025-07-29T07:44:38.134087Z","client_ip":"127.0.0.1"},"memory_usage":4194304,"memory_peak":4194304,"timestamp":"2025-07-29T07:44:38.177256Z"} 
[2025-07-29 15:44:38] local.INFO: [PROCESS] 開始處理PDF壓縮任務 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","job_id":"","task_status":"processing","parameters":{"exam_id":"2","exam_year":"114","test_mode":"large_zip","requested_at":"2025-07-29T07:44:38.134087Z","client_ip":"127.0.0.1"},"test_mode":"large_zip","step":"initialization"} 
[2025-07-29 15:44:38] local.INFO: [STEP1] 獲取推薦函資料 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","step":"get_recommendations","test_mode":"large_zip","parameters":{"exam_id":"2","exam_year":"114","test_mode":"large_zip","requested_at":"2025-07-29T07:44:38.134087Z","client_ip":"127.0.0.1"}} 
[2025-07-29 15:44:38] local.INFO: [TEST] 生成測試推薦函數據 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","test_mode":"large_zip","file_count":10} 
[2025-07-29 15:44:38] local.INFO: [SUCCESS] 成功獲取推薦函 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","recommendation_count":10,"step":"get_recommendations_success"} 
[2025-07-29 15:44:38] local.INFO: [STEP2] 按考生分組 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","step":"group_by_applicant"} 
[2025-07-29 15:44:38] local.INFO: [SUCCESS] 考生分組完成 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","applicant_count":4,"step":"group_by_applicant_success"} 
[2025-07-29 15:44:38] local.INFO: [STEP3] 準備檔案列表 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","step":"prepare_files","applicant_count":4} 
[2025-07-29 15:44:38] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","applicant_id":"test_autono_1","recommendation_count":3,"progress":1,"total":4} 
[2025-07-29 15:44:38] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","recommendation_id":"test_rec_1","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_1.pdf","autono":"test_autono_1"} 
[2025-07-29 15:44:38] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","recommendation_id":"test_rec_2","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_2.pdf","autono":"test_autono_1"} 
[2025-07-29 15:44:38] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","recommendation_id":"test_rec_3","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_3.pdf","autono":"test_autono_1"} 
[2025-07-29 15:44:38] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","applicant_id":"test_autono_1","file_count":3} 
[2025-07-29 15:44:38] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","applicant_id":"test_autono_2","recommendation_count":3,"progress":2,"total":4} 
[2025-07-29 15:44:38] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","recommendation_id":"test_rec_4","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_4.pdf","autono":"test_autono_2"} 
[2025-07-29 15:44:38] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","recommendation_id":"test_rec_5","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_5.pdf","autono":"test_autono_2"} 
[2025-07-29 15:44:38] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","recommendation_id":"test_rec_6","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_6.pdf","autono":"test_autono_2"} 
[2025-07-29 15:44:38] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","applicant_id":"test_autono_2","file_count":3} 
[2025-07-29 15:44:38] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","applicant_id":"test_autono_3","recommendation_count":3,"progress":3,"total":4} 
[2025-07-29 15:44:38] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","recommendation_id":"test_rec_7","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_7.pdf","autono":"test_autono_3"} 
[2025-07-29 15:44:38] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","recommendation_id":"test_rec_8","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_8.pdf","autono":"test_autono_3"} 
[2025-07-29 15:44:38] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","recommendation_id":"test_rec_9","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_9.pdf","autono":"test_autono_3"} 
[2025-07-29 15:44:38] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","applicant_id":"test_autono_3","file_count":3} 
[2025-07-29 15:44:38] local.DEBUG: [PROCESS] 處理考生檔案 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","applicant_id":"test_autono_4","recommendation_count":1,"progress":4,"total":4} 
[2025-07-29 15:44:38] local.DEBUG: [TEST] 使用測試PDF檔案 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","recommendation_id":"test_rec_10","test_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/test_pdfs/test_test_rec_10.pdf","autono":"test_autono_4"} 
[2025-07-29 15:44:38] local.DEBUG: [SUCCESS] 考生檔案準備完成 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","applicant_id":"test_autono_4","file_count":1} 
[2025-07-29 15:44:38] local.INFO: [STATS] 檔案準備統計 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","total_files":10,"processed_applicants":4,"step":"prepare_files_complete"} 
[2025-07-29 15:44:38] local.INFO: [STEP4] 開始建立ZIP檔案 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","step":"create_zip","file_count":10,"test_mode":"large_zip","memory_usage":4194304} 
[2025-07-29 15:44:38] local.INFO: [LARGE_ZIP] 開始創建3GB大型ZIP檔案 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","target_size":"3GB"} 
[2025-07-29 15:44:38] local.INFO: [LARGE_ZIP] 開始寫入大型檔案 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","target_size_bytes":3221225472,"chunk_size_bytes":1048576} 
[2025-07-29 15:44:39] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","progress":"10%","written_bytes":322961408,"written_mb":308.0} 
[2025-07-29 15:44:41] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","progress":"20%","written_bytes":644874240,"written_mb":615.0} 
[2025-07-29 15:44:42] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","progress":"30%","written_bytes":966787072,"written_mb":922.0} 
[2025-07-29 15:44:44] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","progress":"40%","written_bytes":1288699904,"written_mb":1229.0} 
[2025-07-29 15:44:46] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","progress":"50%","written_bytes":1610612736,"written_mb":1536.0} 
[2025-07-29 15:44:47] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","progress":"60%","written_bytes":1933574144,"written_mb":1844.0} 
[2025-07-29 15:44:48] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","progress":"70%","written_bytes":2255486976,"written_mb":2151.0} 
[2025-07-29 15:44:50] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","progress":"80%","written_bytes":2577399808,"written_mb":2458.0} 
[2025-07-29 15:44:51] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","progress":"90%","written_bytes":2899312640,"written_mb":2765.0} 
[2025-07-29 15:44:53] local.INFO: [LARGE_ZIP] 寫入進度 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","progress":"100%","written_bytes":3221225472,"written_mb":3072.0} 
[2025-07-29 15:44:53] local.INFO: [LARGE_ZIP] 大型ZIP檔案創建完成 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","file_path":"pdf_packages/large_test_merge_XEBvlqZ6NkXbKO4W_1753775078.zip","final_size_bytes":3221225472,"final_size_gb":3.0} 
[2025-07-29 15:44:53] local.INFO: [SUCCESS] ZIP檔案建立完成 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","zip_file_path":"pdf_packages/large_test_merge_XEBvlqZ6NkXbKO4W_1753775078.zip","step":"create_zip_success"} 
[2025-07-29 15:44:53] local.INFO: [STEP5] 生成下載URL {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","step":"generate_download_url"} 
[2025-07-29 15:44:53] local.INFO: [SUCCESS] 下載URL生成完成 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","download_url":"https://rec-letter.test/api/public/pdf-download/merge_XEBvlqZ6NkXbKO4W_1753775078","step":"generate_download_url_success"} 
[2025-07-29 15:44:53] local.INFO: [STEP6] 標記任務完成 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","step":"mark_as_ready"} 
[2025-07-29 15:44:53] local.INFO: [COMPLETE] PDF壓縮任務完成 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","job_id":"","compressed_files":10,"zip_file":"pdf_packages/large_test_merge_XEBvlqZ6NkXbKO4W_1753775078.zip","download_url":"https://rec-letter.test/api/public/pdf-download/merge_XEBvlqZ6NkXbKO4W_1753775078","total_execution_time":15.246652841567993,"memory_peak":6291456,"step":"job_complete"} 
[2025-07-29 15:44:53] local.INFO: [DISPATCH] PDF壓縮任務已派發 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","queue_connection":"sync","execution_mode":"immediate (sync)"} 
[2025-07-29 15:44:53] local.INFO: [START] PDF壓縮任務已啟動 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078"} 
[2025-07-29 15:44:58] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-29 15:44:58] local.INFO: [REQUEST] 收到任務狀態查詢請求 {"query":{"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078"},"input":{"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078"},"ip":"127.0.0.1"} 
[2025-07-29 15:44:58] local.INFO: [RESPONSE] 回傳任務狀態 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","status":"ready","message":"任務完成，可以下載","ip":"127.0.0.1"} 
[2025-07-29 15:44:59] local.INFO: 公共PDF壓縮檔案下載 {"task_id":"merge_XEBvlqZ6NkXbKO4W_1753775078","file_path":"pdf_packages/large_test_merge_XEBvlqZ6NkXbKO4W_1753775078.zip","client_ip":"127.0.0.1","user_agent":"curl/8.13.0"} 
