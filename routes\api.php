<?php

use App\Http\Controllers\Auth\ApplicantLoginController;
use App\Http\Controllers\Api\RecommendationApiController;
use App\Http\Controllers\Api\PdfMergeApiController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API 路由
|--------------------------------------------------------------------------
|
| 注意：Laravel 會自動為 api.php 添加 /api 前綴
|
| 已實作API白名單中間件，僅允許來自授權系統的請求
| 因推薦函資料位於此系統，所以須開放給API系統索取資料
*/

/**
 * 報名系統考生登入
 *
 * 將從報名系統收到的 token 送去 API系統 進行解碼和驗證
 * 取得考生資料後登入後建立或更新考生，並登入系統
 */
Route::get('/auth-from-external', [ApplicantLoginController::class, 'handleExternalAuth'])->middleware(['web', 'api.whitelist'])->name('api.auth.external');

/**
 * 受保護的API路由群組
 *
 * 需要通過API白名單驗證才能訪問
 */
Route::middleware(['api.whitelist'])->group(function () {

    /** 健康檢查 - 檢查API服務狀態 */
    Route::get('/health', [RecommendationApiController::class, 'health'])->name('api.health');

    /** API資訊 - 取得API版本和端點資訊 */
    Route::get('/info', [RecommendationApiController::class, 'info'])->name('api.info');

    /** 推薦函相關API */
    Route::prefix('recommendations')->name('api.recommendations.')->group(function () {
        /** 查詢推薦函統計資料 */
        Route::get('/stats', [RecommendationApiController::class, 'getRecommendationStats'])->name('stats');
    });

    /** PDF壓縮相關API */
    Route::prefix('pdf-merge')->name('api.pdf-merge.')->group(function () {
        /**
         * 啟動PDF壓縮任務
         * 支援測試模式參數：
         * - test_mode: boolean (啟用測試模式，產生約3GB檔案)
         */
        Route::post('/start-merge', [PdfMergeApiController::class, 'startMerge'])->name('start');

        /** 查詢壓縮任務狀態 */
        Route::get('/merge-status', [PdfMergeApiController::class, 'getMergeStatus'])->name('status');

        /** 清理過期任務（內部使用） */
        Route::post('/cleanup', [PdfMergeApiController::class, 'cleanupExpiredTasks'])->name('cleanup');
    });
});

/** 公共下載路由（無需認證，用於外部系統直接下載） */
Route::get('/public/pdf-download/{taskId}', [PdfMergeApiController::class, 'publicDownload'])
    ->name('api.pdf-merge.public-download')
    ->where('taskId', '[a-zA-Z0-9_-]+');
