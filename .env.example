APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=sqlite
# DB_HOST=127.0.0.1
# DB_PORT=3306
# DB_DATABASE=laravel
# DB_USERNAME=root
# DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
# QUEUE_CONNECTION=database
# QUEUE_CONNECTION=sync # 同步執行(當派發時就執行)

CACHE_STORE=database
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# 外部郵件 API 設定
MAIL_EXTERNAL_API_ENABLED=true
MAIL_EXTERNAL_API_URL= # http://example.com/sendmail.php
MAIL_EXTERNAL_API_ACCOUNT= # 帳號 
MAIL_EXTERNAL_API_PASSWORD= # 密碼
MAIL_EXTERNAL_API_REPLY_TO="${MAIL_FROM_ADDRESS}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

# API系統整合設定
API_BASE_URL=http://localhost:18001/index.php/api/v1/recommendation_system
API_SECRET=OdmzICpznuM7O48V3gJCJaejNWwabcpG
API_TIMEOUT=30

# API白名單設定
API_WHITELIST_IPS=127.0.0.1,::1,localhost,***********/24
API_WHITELIST_USER_AGENTS=Laravel-RecommendationSystem,ExternalSystem-API,curl

# PDF設定
PDF_ENGINE=dompdf
PDF_PAPER_SIZE=A4
PDF_ORIENTATION=portrait
PDF_DPI=96
PDF_DEFAULT_FONT="DejaVu Sans"
PDF_CHINESE_FONT="Microsoft JhengHei"
PDF_STORAGE_DISK=local
PDF_STORAGE_PATH=recommendations
PDF_MAX_SIZE=10240

# 上線前須知
# 1. 請重新產生APP_KEY: php artisan key:generate
# 2. 請更新API_SECRET為新的密鑰
# 3. 請確認API_BASE_URL指向正確的外部系統API端點
# 4. 請設定正確的API_WHITELIST_IPS，限制只有特定IP可以訪問API

