<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

// 排程自動狀態更新任務
Schedule::command('recommendations:update-status')
    ->hourly()
    ->withoutOverlapping()
    ->runInBackground();

// 排程自動提醒信發送任務 - 每天早上9點執行
Schedule::command('recommendations:send-reminders')
    ->dailyAt('09:00')
    ->withoutOverlapping()
    ->runInBackground();

// 排程重試失敗郵件任務 - 每4小時執行一次
Schedule::command('emails:retry-failed')
    ->everyFourHours()
    ->withoutOverlapping()
    ->runInBackground();

// 排程清理PDF檔案任務 - 每天凌晨2點執行
Schedule::command('pdf:cleanup --days=7')
    ->dailyAt('02:00')
    ->withoutOverlapping()
    ->runInBackground();
