# PDF合併API測試模式文檔

## 概述

PDF合併API現在支援多種測試模式，用於模擬不同的處理情境和壓力測試。

## API端點

### 啟動PDF壓縮任務

**POST** `/api/pdf-merge/start-merge`

#### 基本參數

| 參數 | 類型 | 必填 | 說明 |
|------|------|------|------|
| exam_id | string | 是 | 考試ID (最大50字符) |
| exam_year | integer | 是 | 考試年度 (100-200) |

#### 測試模式參數

| 參數 | 類型 | 必填 | 說明 |
|------|------|------|------|
| test_mode | string | 否 | 測試模式類型 |
| test_file_count | integer | 否 | 測試檔案數量 (1-1000) |
| test_delay_seconds | integer | 否 | 測試延遲秒數 (1-300) |

#### 測試模式類型

##### 1. stress_test (壓力測試)
- **用途**: 模擬大量檔案處理
- **預設檔案數量**: 100
- **特點**: 測試系統在高負載下的穩定性

##### 2. large_files (大檔案測試)
- **用途**: 模擬處理大型檔案
- **預設檔案數量**: 50
- **特點**: 測試記憶體使用和處理效能

##### 3. slow_processing (慢速處理測試)
- **用途**: 模擬長時間處理情境
- **預設檔案數量**: 20
- **特點**: 可配合test_delay_seconds參數模擬處理延遲

##### 4. transmission_test (傳輸測試)
- **用途**: 測試傳輸穩定性
- **預設檔案數量**: 30
- **特點**: 30%機率模擬傳輸問題，回傳替代方案

## 請求範例

### 基本請求
```json
{
    "exam_id": "TEST2024",
    "exam_year": 113
}
```

### 壓力測試
```json
{
    "exam_id": "TEST2024",
    "exam_year": 113,
    "test_mode": "stress_test",
    "test_file_count": 200
}
```

### 慢速處理測試
```json
{
    "exam_id": "TEST2024",
    "exam_year": 113,
    "test_mode": "slow_processing",
    "test_file_count": 10,
    "test_delay_seconds": 30
}
```

### 傳輸穩定性測試
```json
{
    "exam_id": "TEST2024",
    "exam_year": 113,
    "test_mode": "transmission_test"
}
```

## 回應格式

### 成功回應
```json
{
    "status": "processing",
    "task_id": "merge_abc123_1640995200",
    "message": "壓縮任務已啟動，請使用task_id查詢進度"
}
```

### 傳輸問題回應 (transmission_test模式)
```json
{
    "status": "transmission_error",
    "message": "檢測到傳輸不穩定，建議使用替代方案",
    "details": {
        "issue": "網路延遲過高",
        "alternative_methods": {
            "direct_download": "直接下載個別檔案",
            "batch_processing": "分批處理",
            "retry_later": "稍後重試"
        },
        "retry_after": 300
    }
}
```

### 錯誤回應
```json
{
    "status": "error",
    "message": "參數驗證失敗",
    "errors": {
        "test_mode": ["測試模式必須是: stress_test, large_files, slow_processing, transmission_test"]
    }
}
```

## 查詢任務狀態

**GET** `/api/pdf-merge/merge-status?task_id={task_id}`

測試模式下的任務狀態查詢與正常模式相同，但會在日誌中標記為測試模式。

## 日誌格式

測試模式下的日誌使用大括號標記不同的操作類型：

- `[TEST]` - 測試模式相關操作
- `[START]` - 任務開始
- `[PROCESS]` - 處理中
- `[SUCCESS]` - 成功完成
- `[ERROR]` - 錯誤
- `[WARNING]` - 警告
- `[TRANSMISSION]` - 傳輸相關

## 注意事項

1. 測試模式僅用於開發和測試環境
2. 測試模式會創建虛擬PDF檔案，不會使用真實的推薦函資料
3. 傳輸測試模式有30%機率回傳傳輸錯誤
4. 所有測試檔案會存放在 `storage/app/test_pdfs/` 目錄下
5. 測試模式的任務過期時間與正常模式相同（24小時）

## 故障排除

### 常見問題

1. **測試檔案創建失敗**
   - 檢查 `storage/app/test_pdfs/` 目錄權限
   - 確保有足夠的磁碟空間

2. **測試模式參數無效**
   - 檢查test_mode是否為支援的類型
   - 確認test_file_count和test_delay_seconds在有效範圍內

3. **傳輸測試總是失敗**
   - 這是正常行為，傳輸測試有30%機率模擬失敗
   - 多次嘗試可以測試不同情境
