<?php

namespace App\Jobs;

use App\Models\PdfMergeTask;
use App\Models\RecommendationLetter;
use App\Services\PdfService;
use App\Services\FilePackagingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

/**
 * PDF壓縮處理Job
 *
 * 處理PDF壓縮任務，包含檔案打包和壓縮
 */
class ProcessPdfMergeJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 任務超時時間（秒）
     */
    public $timeout = 1800; // 30分鐘

    /**
     * 最大重試次數
     */
    public $tries = 1; // 1表示不重試，直接失敗

    /**
     * 任務ID
     */
    protected string $taskId;

    /**
     * 合併參數
     */
    protected array $parameters;

    /**
     * 創建新的Job實例
     *
     * @param string $taskId 任務ID
     * @param array $parameters 壓縮參數
     */
    public function __construct(string $taskId, array $parameters)
    {
        $this->taskId = $taskId;
        $this->parameters = $parameters;
    }

    /**
     * 執行Job
     *
     * @return void
     */
    public function handle(): void
    {
        // 記錄任務開始
        Log::info('[JOB_START]', [
            'task_id' => $this->taskId,
            'test_mode' => !empty($this->parameters['test_mode'])
        ]);

        $task = PdfMergeTask::findByTaskId($this->taskId);

        if (!$task) {
            Log::error('[JOB_ERROR]', ['task_id' => $this->taskId, 'error' => 'task_not_found']);
            return;
        }

        try {
            $isTestMode = !empty($this->parameters['test_mode']);

            // 測試模式處理時間延遲 2分鐘
            // if ($isTestMode) {
            //     $processingMinutes = 2;
            //     $delaySeconds = $processingMinutes * 60;
            //     Log::info('[TEST_DELAY]', [
            //         'task_id' => $this->taskId,
            //         'processing_minutes' => $processingMinutes
            //     ]);

            //     sleep($delaySeconds);
            // }

            if ($isTestMode) {
                $recommendations = $this->generateTestRecommendations();
            } else {
                $recommendations = $this->getRecommendationsToMerge();
            }

            if ($recommendations->isEmpty()) {
                throw new \Exception('沒有找到需要壓縮的推薦函');
            }

            // 將推薦函依考生autono分組
            $groupedByRecommendationsAutono = $this->groupRecommendationsByExternalAutono($recommendations);

            if (empty($groupedByRecommendationsAutono)) {
                Log::error('[ERROR] 分組後沒有資料', [
                    'task_id' => $this->taskId,
                    'original_count' => $recommendations->count()
                ]);
                throw new \Exception('沒有找到需要壓縮的推薦函或考生資料');
            }

            Log::info('[SUCCESS] 考生分組完成', [
                'task_id' => $this->taskId,
                'applicant_count' => count($groupedByRecommendationsAutono),
                'step' => 'group_by_applicant_success'
            ]);

            // 更新任務進度
            $task->updateProgress(0, count($groupedByRecommendationsAutono));

            $filesToCompress = [];
            $processedCount = 0;

            // 遍歷每個考生的推薦函，準備檔案列表
            foreach ($groupedByRecommendationsAutono as $applicantId => $applicantRecommendations) {
                try {
                    // 獲取考生的所有推薦函PDF檔案
                    $applicantFiles = $this->prepareApplicantFiles($applicantRecommendations);

                    // 如果考生有檔案，則添加到壓縮列表
                    if (!empty($applicantFiles)) {
                        $filesToCompress = array_merge($filesToCompress, $applicantFiles);
                    }

                    $processedCount++;
                    $task->updateProgress($processedCount);
                } catch (\Exception $e) {
                    // 繼續處理其他考生
                }
            }

            if (empty($filesToCompress)) {
                throw new \Exception('沒有找到任何可壓縮的PDF檔案');
            }

            // 使用FilePackagingService進行壓縮
            $packagingService = new FilePackagingService();
            $zipFilePath = $packagingService->createDirectZipPackage(
                $filesToCompress,
                $this->taskId,
                $this->parameters
            );

            $downloadUrl = $this->generateDownloadUrl();

            $task->markAsReady($zipFilePath, $downloadUrl);

            Log::info('[JOB_COMPLETE]', [
                'task_id' => $this->taskId,
                'file_count' => count($filesToCompress),
                'zip_size_mb' => file_exists(Storage::disk('public')->path($zipFilePath))
                    ? round(filesize(Storage::disk('public')->path($zipFilePath)) / (1024 * 1024), 2)
                    : 0
            ]);
        } catch (\Exception $e) {
            Log::error('[JOB_FAILED]', [
                'task_id' => $this->taskId,
                'error' => $e->getMessage(),
                'error_line' => $e->getLine()
            ]);

            $task->markAsFailed($e->getMessage());
        }
    }

    /**
     * 獲取需要合併的推薦函
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    protected function getRecommendationsToMerge()
    {
        $query = RecommendationLetter::where('status', RecommendationLetter::STATUS_SUBMITTED)
            ->whereNotNull('pdf_path');

        // 根據參數過濾
        if (isset($this->parameters['exam_id'])) {
            $query->where('exam_id', $this->parameters['exam_id']);
        }

        if (isset($this->parameters['exam_year'])) {
            $query->where('exam_year', $this->parameters['exam_year']);
        }

        return $query->with(['applicant', 'recommender'])->get();
    }

    /**
     * 按考生autono分組推薦函
     *
     * @param \Illuminate\Database\Eloquent\Collection $recommendations
     * @return array
     */
    protected function groupRecommendationsByExternalAutono($recommendations): array
    {
        return $recommendations->groupBy('external_autono')->toArray();
    }

    /**
     * 準備考生的檔案列表
     *
     * @param array $recommendations
     * @return array
     */
    protected function prepareApplicantFiles(array $recommendations): array
    {
        $files = [];

        if (empty($recommendations)) {
            return $files;
        }

        // 獲取考生資訊
        $applicant = $recommendations[0]['applicant'] ?? null;
        if (!$applicant) {
            Log::warning('考生資訊不存在', [
                'recommendations' => count($recommendations)
            ]);
            return $files;
        }

        $autono = $recommendations[0]['external_autono'] ?? $applicant['id'] ?? 'unknown';
        $fileIndex = 1;

        foreach ($recommendations as $recommendation) {
            $pdfPath = $recommendation['pdf_path'] ?? null;
            $recId = $recommendation['id'] ?? null;

            if (empty($pdfPath)) {
                continue;
            }

            $disk = Storage::disk('local');

            if (!$disk->exists($pdfPath)) {
                Log::warning('PDF檔案不存在', [
                    'recommendation_id' => $recId,
                    'pdf_path' => $pdfPath,
                    'full_path' => $disk->path($pdfPath)
                ]);
                continue;
            }

            $actualPath = $disk->path($pdfPath);

            // 建立檔案輸出路徑
            $fileName = "{$fileIndex}.pdf";

            $files[] = [
                'source_path' => $actualPath,
                'zip_path' => "{$autono}/{$fileName}",
                'applicant_id' => $recommendation['applicant_id'],
                'autono' => $autono,
                'recommendation_id' => $recId,
                'original_pdf_path' => $pdfPath,
                'file_index' => $fileIndex
            ];

            $fileIndex++;
        }

        return $files;
    }

    /**
     * 生成下載URL
     *
     * @return string
     */
    protected function generateDownloadUrl(): string
    {
        // 生成公共下載 URL（無需認證）
        return route('api.pdf-merge.public-download', [
            'taskId' => $this->taskId
        ]);
    }

    /**
     * Job失敗時的處理
     *
     * @param \Throwable $exception
     * @return void
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('[FINAL_FAILED] PDF壓縮Job最終失敗', [
            'task_id' => $this->taskId,
            'job_id' => $this->job->getJobId() ?? 'unknown',
            'exception_class' => get_class($exception),
            'error' => $exception->getMessage(),
            'error_code' => $exception->getCode(),
            'error_file' => $exception->getFile(),
            'error_line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
            'attempts' => $this->attempts(),
            'max_tries' => $this->tries,
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
            'parameters' => $this->parameters,
            'timestamp' => now()->toISOString(),
            'step' => 'job_failed_final'
        ]);

        $task = PdfMergeTask::findByTaskId($this->taskId);

        if ($task) {
            $task->markAsFailed($exception->getMessage());
            Log::info('[UPDATE] 任務狀態已更新為失敗', [
                'task_id' => $this->taskId,
                'task_status' => $task->status
            ]);
        } else {
            Log::error('[ERROR] 無法找到任務以更新失敗狀態', [
                'task_id' => $this->taskId
            ]);
        }
    }

    /**
     * 生成測試模式的PDF壓縮數據
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    protected function generateTestRecommendations()
    {
        // 計算達到3GB所需的PDF數量（假設每個PDF約2MB）
        $targetSizeGB = 3;
        $avgPdfSizeMB = 2;
        $fileCount = intval(($targetSizeGB * 1024) / $avgPdfSizeMB); // 約1536個檔案

        Log::info('[TEST_GENERATE]', [
            'task_id' => $this->taskId,
            'target_size_gb' => $targetSizeGB,
            'file_count' => $fileCount
        ]);

        $testRecommendations = collect();

        for ($i = 1; $i <= $fileCount; $i++) {
            $applicantId = 'test_applicant_' . ceil($i / 3);
            $externalAutono = 'test_autono_' . ceil($i / 3);

            $testRecommendations->push((object)[
                'id' => 'test_rec_' . $i,
                'applicant_id' => $applicantId,
                'external_autono' => $externalAutono,
                'pdf_path' => 'test_pdfs/large_test_recommendation_' . $i . '.pdf',
                'status' => 'submitted',
                'applicant' => (object)[
                    'id' => $applicantId,
                    'name' => '測試考生' . ceil($i / 3)
                ],
                'recommender' => (object)[
                    'id' => 'test_recommender_' . $i,
                    'name' => '測試推薦人' . $i
                ]
            ]);
        }

        return $testRecommendations;
    }

    /**
     * 創建測試PDF檔案路徑
     *
     * @param string $recommendationId
     * @return string
     */
    protected function createTestPdfPath(string $recommendationId): string
    {
        // 創建測試檔案目錄
        $testDir = storage_path('app/test_pdfs');
        if (!file_exists($testDir)) {
            mkdir($testDir, 0755, true);
        }

        $testFilePath = $testDir . '/test_' . $recommendationId . '.pdf';

        // 如果測試檔案不存在，創建一個簡單的PDF檔案
        if (!file_exists($testFilePath)) {
            $this->createSimpleTestPdf($testFilePath, $recommendationId);
        }

        return $testFilePath;
    }

    /**
     * 創建大型測試PDF檔案（約2MB）
     *
     * @param string $filePath
     * @param string $recommendationId
     * @return void
     */
    protected function createSimpleTestPdf(string $filePath, string $recommendationId): void
    {
        // 創建約2MB的PDF檔案
        $targetSizeMB = 2;
        $targetSizeBytes = $targetSizeMB * 1024 * 1024;

        // 基本PDF結構
        $pdfContent = "%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n";
        $pdfContent .= "2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n";
        $pdfContent .= "3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\n";

        // 添加大量內容以達到目標大小
        $fillContent = str_repeat(
            "% Large PDF test content for recommendation {$recommendationId}\n",
            intval($targetSizeBytes / 100)
        );
        $pdfContent .= $fillContent;

        $pdfContent .= "xref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000074 00000 n \n0000000120 00000 n \n";
        $pdfContent .= "trailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n179\n%%EOF";

        file_put_contents($filePath, $pdfContent);

        Log::info('[TEST_PDF]', [
            'task_id' => $this->taskId,
            'file_size_mb' => round(filesize($filePath) / (1024 * 1024), 2)
        ]);
    }
}
