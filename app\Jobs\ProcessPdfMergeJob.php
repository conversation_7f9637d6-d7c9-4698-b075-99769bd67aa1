<?php

namespace App\Jobs;

use App\Models\PdfMergeTask;
use App\Models\RecommendationLetter;
use App\Services\PdfService;
use App\Services\FilePackagingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

/**
 * PDF合併處理Job
 * 
 * 處理PDF合併任務，包含書籤插入和檔案打包
 */
class ProcessPdfMergeJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 任務超時時間（秒）
     */
    public $timeout = 1800; // 30分鐘

    /**
     * 最大重試次數
     */
    public $tries = 3;

    /**
     * 任務ID
     */
    protected string $taskId;

    /**
     * 合併參數
     */
    protected array $parameters;

    /**
     * 創建新的Job實例
     *
     * @param string $taskId 任務ID
     * @param array $parameters 合併參數
     */
    public function __construct(string $taskId, array $parameters)
    {
        $this->taskId = $taskId;
        $this->parameters = $parameters;
    }

    /**
     * 執行Job
     *
     * @return void
     */
    public function handle(): void
    {
        // 記錄 Job 開始執行
        Log::info('[START] PDF壓縮Job開始執行', [
            'task_id' => $this->taskId,
            'job_id' => $this->job->getJobId() ?? 'unknown',
            'queue' => $this->job->getQueue() ?? 'unknown',
            'attempts' => $this->attempts(),
            'max_tries' => $this->tries,
            'timeout' => $this->timeout,
            'parameters' => $this->parameters,
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
            'timestamp' => now()->toISOString()
        ]);

        $task = PdfMergeTask::findByTaskId($this->taskId);

        if (!$task) {
            Log::error('[ERROR] PDF壓縮任務不存在', [
                'task_id' => $this->taskId,
                'job_id' => $this->job->getJobId() ?? 'unknown'
            ]);
            return;
        }

        try {
            // 檢查是否為測試模式
            $isTestMode = !empty($this->parameters['test_mode']);
            $testMode = $this->parameters['test_mode'] ?? null;

            Log::info('[PROCESS] 開始處理PDF壓縮任務', [
                'task_id' => $this->taskId,
                'job_id' => $this->job->getJobId() ?? 'unknown',
                'task_status' => $task->status,
                'parameters' => $this->parameters,
                'test_mode' => $testMode,
                'step' => 'initialization'
            ]);

            // 測試模式延遲處理
            if ($isTestMode && isset($this->parameters['test_delay_seconds'])) {
                $delaySeconds = min(300, max(1, $this->parameters['test_delay_seconds']));
                Log::info('[TEST] 模擬處理延遲', [
                    'task_id' => $this->taskId,
                    'delay_seconds' => $delaySeconds
                ]);
                sleep($delaySeconds);
            }

            // 步驟1: 獲取需要壓縮的推薦函
            Log::info('[STEP1] 獲取推薦函資料', [
                'task_id' => $this->taskId,
                'step' => 'get_recommendations',
                'test_mode' => $testMode,
                'parameters' => $this->parameters
            ]);

            if ($isTestMode) {
                $recommendations = $this->generateTestRecommendations();
            } else {
                $recommendations = $this->getRecommendationsToMerge();
            }

            if ($recommendations->isEmpty()) {
                Log::warning('[WARNING] 沒有找到需要壓縮的推薦函', [
                    'task_id' => $this->taskId,
                    'parameters' => $this->parameters
                ]);
                throw new \Exception('沒有找到需要壓縮的推薦函');
            }

            Log::info('[SUCCESS] 成功獲取推薦函', [
                'task_id' => $this->taskId,
                'recommendation_count' => $recommendations->count(),
                'step' => 'get_recommendations_success'
            ]);

            // 步驟2: 按考生分組
            Log::info('[STEP2] 按考生分組', [
                'task_id' => $this->taskId,
                'step' => 'group_by_applicant'
            ]);

            $groupedByRecommendationsAutono = $this->groupRecommendationsByExternalAutono($recommendations);

            if (empty($groupedByRecommendationsAutono)) {
                Log::error('[ERROR] 分組後沒有資料', [
                    'task_id' => $this->taskId,
                    'original_count' => $recommendations->count()
                ]);
                throw new \Exception('沒有找到需要壓縮的推薦函或考生資料');
            }

            Log::info('[SUCCESS] 考生分組完成', [
                'task_id' => $this->taskId,
                'applicant_count' => count($groupedByRecommendationsAutono),
                'step' => 'group_by_applicant_success'
            ]);

            $task->updateProgress(0, count($groupedByRecommendationsAutono));

            // 步驟3: 準備要壓縮的檔案列表（不進行合併）
            Log::info('[STEP3] 準備檔案列表', [
                'task_id' => $this->taskId,
                'step' => 'prepare_files',
                'applicant_count' => count($groupedByRecommendationsAutono)
            ]);

            $filesToCompress = [];
            $processedCount = 0;

            foreach ($groupedByRecommendationsAutono as $applicantId => $applicantRecommendations) {
                try {
                    Log::debug('[PROCESS] 處理考生檔案', [
                        'task_id' => $this->taskId,
                        'applicant_id' => $applicantId,
                        'recommendation_count' => count($applicantRecommendations),
                        'progress' => $processedCount + 1,
                        'total' => count($groupedByRecommendationsAutono)
                    ]);

                    // 獲取考生的所有推薦函PDF檔案
                    $applicantFiles = $this->prepareApplicantFiles($applicantRecommendations);

                    if (!empty($applicantFiles)) {
                        $filesToCompress = array_merge($filesToCompress, $applicantFiles);
                        Log::debug('[SUCCESS] 考生檔案準備完成', [
                            'task_id' => $this->taskId,
                            'applicant_id' => $applicantId,
                            'file_count' => count($applicantFiles)
                        ]);
                    } else {
                        Log::warning('[WARNING] 考生沒有可用檔案', [
                            'task_id' => $this->taskId,
                            'applicant_id' => $applicantId
                        ]);
                    }

                    $processedCount++;
                    $task->updateProgress($processedCount);
                } catch (\Exception $e) {
                    Log::error('[ERROR] 考生檔案準備失敗', [
                        'task_id' => $this->taskId,
                        'applicant_id' => $applicantId,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                    // 繼續處理其他考生
                }
            }

            Log::info('[STATS] 檔案準備統計', [
                'task_id' => $this->taskId,
                'total_files' => count($filesToCompress),
                'processed_applicants' => $processedCount,
                'step' => 'prepare_files_complete'
            ]);

            if (empty($filesToCompress)) {
                Log::error('[ERROR] 沒有找到任何可壓縮的PDF檔案', [
                    'task_id' => $this->taskId,
                    'processed_applicants' => $processedCount
                ]);
                throw new \Exception('沒有找到任何可壓縮的PDF檔案');
            }

            // 步驟4: 直接打包成ZIP檔案（不合併）
            Log::info('[STEP4] 開始建立ZIP檔案', [
                'task_id' => $this->taskId,
                'step' => 'create_zip',
                'file_count' => count($filesToCompress),
                'memory_usage' => memory_get_usage(true)
            ]);

            $packagingService = new FilePackagingService();
            $zipFilePath = $packagingService->createDirectZipPackage(
                $filesToCompress,
                $this->taskId,
                $this->parameters
            );

            Log::info('[SUCCESS] ZIP檔案建立完成', [
                'task_id' => $this->taskId,
                'zip_file_path' => $zipFilePath,
                'step' => 'create_zip_success'
            ]);

            // 步驟5: 生成下載URL（用於API訪問）
            Log::info('[STEP5] 生成下載URL', [
                'task_id' => $this->taskId,
                'step' => 'generate_download_url'
            ]);

            $downloadUrl = $this->generateDownloadUrl();

            Log::info('[SUCCESS] 下載URL生成完成', [
                'task_id' => $this->taskId,
                'download_url' => $downloadUrl,
                'step' => 'generate_download_url_success'
            ]);

            // 步驟6: 標記任務完成
            Log::info('[STEP6] 標記任務完成', [
                'task_id' => $this->taskId,
                'step' => 'mark_as_ready'
            ]);

            $task->markAsReady($zipFilePath, $downloadUrl);

            Log::info('[COMPLETE] PDF壓縮任務完成', [
                'task_id' => $this->taskId,
                'job_id' => $this->job->getJobId() ?? 'unknown',
                'compressed_files' => count($filesToCompress),
                'zip_file' => $zipFilePath,
                'download_url' => $downloadUrl,
                'total_execution_time' => microtime(true) - LARAVEL_START,
                'memory_peak' => memory_get_peak_usage(true),
                'step' => 'job_complete'
            ]);
        } catch (\Exception $e) {
            Log::error('[FAILED] PDF壓縮任務失敗', [
                'task_id' => $this->taskId,
                'job_id' => $this->job->getJobId() ?? 'unknown',
                'error' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
                'memory_usage' => memory_get_usage(true),
                'memory_peak' => memory_get_peak_usage(true),
                'attempts' => $this->attempts(),
                'max_tries' => $this->tries,
                'step' => 'job_failed'
            ]);

            $task->markAsFailed($e->getMessage());
        }
    }

    /**
     * 獲取需要合併的推薦函
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    protected function getRecommendationsToMerge()
    {
        $query = RecommendationLetter::where('status', RecommendationLetter::STATUS_SUBMITTED)
            ->whereNotNull('pdf_path');

        // 根據參數過濾
        if (isset($this->parameters['exam_id'])) {
            $query->where('exam_id', $this->parameters['exam_id']);
        }

        if (isset($this->parameters['exam_year'])) {
            $query->where('exam_year', $this->parameters['exam_year']);
        }

        return $query->with(['applicant', 'recommender'])->get();
    }

    /**
     * 按考生分組推薦函（使用external_autono）
     */
    protected function groupRecommendationsByExternalAutono($recommendations): array
    {
        return $recommendations->groupBy('external_autono')->toArray();
    }

    /**
     * 準備考生的檔案列表（用於直接壓縮）
     *
     * @param array $recommendations
     * @return array
     */
    protected function prepareApplicantFiles(array $recommendations): array
    {
        $files = [];

        if (empty($recommendations)) {
            return $files;
        }

        // 獲取考生資訊
        $applicant = $recommendations[0]['applicant'] ?? null;
        if (!$applicant) {
            Log::warning('考生資訊不存在', [
                'recommendations' => count($recommendations)
            ]);
            return $files;
        }

        $autono = $recommendations[0]['external_autono'] ?? $applicant['id'] ?? 'unknown';

        // 處理每個推薦函的PDF檔案
        $fileIndex = 1; // 為每個推薦函分配索引

        $isTestMode = !empty($this->parameters['test_mode']);

        foreach ($recommendations as $recommendation) {
            if (empty($recommendation['pdf_path'])) {
                continue;
            }

            $pdfPath = $recommendation['pdf_path'];

            if ($isTestMode) {
                // 測試模式：創建虛擬檔案路徑
                $actualPath = $this->createTestPdfPath($recommendation['id']);
                Log::debug('[TEST] 使用測試PDF檔案', [
                    'task_id' => $this->taskId,
                    'recommendation_id' => $recommendation['id'],
                    'test_path' => $actualPath,
                    'autono' => $autono
                ]);
            } else {
                // 正常模式：檢查檔案是否存在
                $disk = Storage::disk('local');

                if (!$disk->exists($pdfPath)) {
                    Log::warning('[WARNING] PDF檔案不存在', [
                        'recommendation_id' => $recommendation['id'],
                        'pdf_path' => $pdfPath,
                        'full_path' => $disk->path($pdfPath)
                    ]);
                    continue;
                }

                // 獲取實際的文件路徑
                $actualPath = $disk->path($pdfPath);
            }

            // 新的檔案命名規則：autono資料夾內以index命名 (348/1.pdf, 348/2.pdf...)
            $fileName = "{$fileIndex}.pdf";

            $files[] = [
                'source_path' => $actualPath,
                'zip_path' => "{$autono}/{$fileName}",
                'applicant_id' => $recommendation['applicant_id'],
                'autono' => $autono,
                'recommendation_id' => $recommendation['id'],
                'original_pdf_path' => $pdfPath,
                'file_index' => $fileIndex
            ];

            $fileIndex++;
        }

        return $files;
    }

    /**
     * 生成下載URL
     *
     * @return string
     */
    protected function generateDownloadUrl(): string
    {
        // 生成公共下載 URL（無需認證）
        return route('api.pdf-merge.public-download', [
            'taskId' => $this->taskId
        ]);
    }

    /**
     * Job失敗時的處理
     *
     * @param \Throwable $exception
     * @return void
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('[FINAL_FAILED] PDF壓縮Job最終失敗', [
            'task_id' => $this->taskId,
            'job_id' => $this->job->getJobId() ?? 'unknown',
            'exception_class' => get_class($exception),
            'error' => $exception->getMessage(),
            'error_code' => $exception->getCode(),
            'error_file' => $exception->getFile(),
            'error_line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
            'attempts' => $this->attempts(),
            'max_tries' => $this->tries,
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
            'parameters' => $this->parameters,
            'timestamp' => now()->toISOString(),
            'step' => 'job_failed_final'
        ]);

        $task = PdfMergeTask::findByTaskId($this->taskId);

        if ($task) {
            $task->markAsFailed($exception->getMessage());
            Log::info('[UPDATE] 任務狀態已更新為失敗', [
                'task_id' => $this->taskId,
                'task_status' => $task->status
            ]);
        } else {
            Log::error('[ERROR] 無法找到任務以更新失敗狀態', [
                'task_id' => $this->taskId
            ]);
        }
    }

    /**
     * 生成測試模式的推薦函數據
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    protected function generateTestRecommendations()
    {
        $testMode = $this->parameters['test_mode'];
        $fileCount = $this->parameters['test_file_count'] ?? 10;

        Log::info('[TEST] 生成測試推薦函數據', [
            'task_id' => $this->taskId,
            'test_mode' => $testMode,
            'file_count' => $fileCount
        ]);

        // 創建模擬的推薦函集合
        $testRecommendations = collect();

        for ($i = 1; $i <= $fileCount; $i++) {
            $applicantId = 'test_applicant_' . ceil($i / 3); // 每3個推薦函對應一個考生
            $externalAutono = 'test_autono_' . ceil($i / 3);

            $testRecommendations->push((object)[
                'id' => 'test_rec_' . $i,
                'applicant_id' => $applicantId,
                'external_autono' => $externalAutono,
                'pdf_path' => 'test_pdfs/test_recommendation_' . $i . '.pdf',
                'status' => 'submitted',
                'applicant' => (object)[
                    'id' => $applicantId,
                    'name' => '測試考生' . ceil($i / 3)
                ],
                'recommender' => (object)[
                    'id' => 'test_recommender_' . $i,
                    'name' => '測試推薦人' . $i
                ]
            ]);
        }

        return $testRecommendations;
    }

    /**
     * 創建測試PDF檔案路徑
     *
     * @param string $recommendationId
     * @return string
     */
    protected function createTestPdfPath(string $recommendationId): string
    {
        // 創建測試檔案目錄
        $testDir = storage_path('app/test_pdfs');
        if (!file_exists($testDir)) {
            mkdir($testDir, 0755, true);
        }

        $testFilePath = $testDir . '/test_' . $recommendationId . '.pdf';

        // 如果測試檔案不存在，創建一個簡單的PDF檔案
        if (!file_exists($testFilePath)) {
            $this->createSimpleTestPdf($testFilePath, $recommendationId);
        }

        return $testFilePath;
    }

    /**
     * 創建簡單的測試PDF檔案
     *
     * @param string $filePath
     * @param string $recommendationId
     * @return void
     */
    protected function createSimpleTestPdf(string $filePath, string $recommendationId): void
    {
        // 創建一個簡單的PDF內容（模擬）
        $pdfContent = "%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n";
        $pdfContent .= "2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n";
        $pdfContent .= "3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\n";
        $pdfContent .= "xref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000074 00000 n \n0000000120 00000 n \n";
        $pdfContent .= "trailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n179\n%%EOF";

        file_put_contents($filePath, $pdfContent);

        Log::info('[TEST] 創建測試PDF檔案', [
            'task_id' => $this->taskId,
            'recommendation_id' => $recommendationId,
            'file_path' => $filePath,
            'file_size' => filesize($filePath)
        ]);
    }
}
