<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

/**
 * PDF合併任務模型
 * 
 * 追蹤PDF合併任務的狀態、進度和結果
 */
class PdfMergeTask extends Model
{
    use HasFactory;

    /**
     * 任務狀態常數
     */
    const STATUS_PROCESSING = 'processing'; // 處理中
    const STATUS_READY = 'ready';          // 完成，可下載
    const STATUS_FAILED = 'failed';        // 失敗
    const STATUS_EXPIRED = 'expired';      // 已過期

    /**
     * 可批量賦值的屬性
     *
     * @var array<string>
     */
    protected $fillable = [
        'task_id',
        'status',
        'progress',
        'parameters',
        'download_url',
        'zip_file_path',
        'total_files',
        'processed_files',
        'error_message',
        'expires_at',
    ];

    /**
     * 屬性類型轉換
     *
     * @var array<string, string>
     */
    protected $casts = [
        'parameters' => 'array',
        'expires_at' => 'datetime',
        'progress' => 'integer',
        'total_files' => 'integer',
        'processed_files' => 'integer',
    ];

    /**
     * 生成新的任務ID
     *
     * @return string
     */
    public static function generateTaskId(): string
    {
        return 'task_' . Str::random(16) . '_' . time();
    }

    /**
     * 創建新的壓縮任務
     *
     * @param array $parameters 壓縮參數
     * @return static
     */
    public static function createTask(array $parameters): static
    {
        // 檢查是否已有相同參數的處理中任務
        $existingTask = static::where('status', static::STATUS_PROCESSING)
            ->where(function ($query) use ($parameters) {
                if (isset($parameters['exam_id'])) {
                    $query->whereJsonContains('parameters->exam_id', $parameters['exam_id']);
                }
                if (isset($parameters['exam_year'])) {
                    $query->whereJsonContains('parameters->exam_year', $parameters['exam_year']);
                }
                if (isset($parameters['test_mode'])) {
                    $query->whereJsonContains('parameters->test_mode', $parameters['test_mode']);
                }
            })
            ->first();

        if ($existingTask) {
            Log::warning('[DUPLICATE_TASK_PREVENTED]', [
                'existing_task_id' => $existingTask->task_id,
                'parameters' => $parameters
            ]);

            throw new \Exception('相同參數的任務正在處理中，請等待完成後再創建新任務。');
        }

        $task = static::create([
            'task_id' => static::generateTaskId(),
            'status' => static::STATUS_PROCESSING,
            'progress' => 0,
            'parameters' => $parameters,
            'expires_at' => now()->addHours(24), // 24小時後過期
        ]);

        Log::info('[TASK_CREATED]', [
            'task_id' => $task->task_id,
            'parameters' => $parameters
        ]);

        return $task;
    }

    /**
     * 更新任務進度
     *
     * @param int $processedFiles 已處理檔案數
     * @param int|null $totalFiles 總檔案數（可選）
     * @return void
     */
    public function updateProgress(int $processedFiles, ?int $totalFiles = null): void
    {
        if ($totalFiles !== null) {
            $this->total_files = $totalFiles;
        }

        $this->processed_files = $processedFiles;

        if ($this->total_files > 0) {
            $this->progress = min(100, intval(($processedFiles / $this->total_files) * 100));
        }

        $this->save();
    }

    /**
     * 標記任務為完成
     *
     * @param string $zipFilePath ZIP檔案路徑
     * @param string $downloadUrl 下載URL
     * @return void
     */
    public function markAsReady(string $zipFilePath, string $downloadUrl): void
    {
        $this->update([
            'status' => static::STATUS_READY,
            'progress' => 100,
            'zip_file_path' => $zipFilePath,
            'download_url' => $downloadUrl,
        ]);
    }

    /**
     * 標記任務為失敗
     *
     * @param string $errorMessage 錯誤訊息
     * @return void
     */
    public function markAsFailed(string $errorMessage): void
    {
        $this->update([
            'status' => static::STATUS_FAILED,
            'error_message' => $errorMessage,
        ]);
    }

    /**
     * 檢查任務是否已過期
     *
     * @return bool
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * 檢查任務是否可以下載
     *
     * @return bool
     */
    public function isReady(): bool
    {
        return $this->status === static::STATUS_READY &&
            !$this->isExpired() &&
            $this->zip_file_path &&
            Storage::disk('public')->exists($this->zip_file_path);
    }

    /**
     * 檢查任務是否正在處理中
     *
     * @return bool
     */
    public function isProcessing(): bool
    {
        return $this->status === static::STATUS_PROCESSING;
    }

    /**
     * 檢查任務是否失敗
     *
     * @return bool
     */
    public function isFailed(): bool
    {
        return $this->status === static::STATUS_FAILED;
    }

    /**
     * 獲取任務狀態的中文描述
     *
     * @return string
     */
    public function getStatusDescription(): string
    {
        return match ($this->status) {
            static::STATUS_PROCESSING => '處理中',
            static::STATUS_READY => '完成',
            static::STATUS_FAILED => '失敗',
            static::STATUS_EXPIRED => '已過期',
            default => '未知狀態',
        };
    }

    /**
     * 清理過期的任務和檔案
     *
     * @return int 清理的任務數量
     */
    public static function cleanupExpiredTasks(): int
    {
        $expiredTasks = static::where('expires_at', '<', now())
            ->orWhere(function ($query) {
                $query->where('status', static::STATUS_READY)
                    ->where('created_at', '<', now()->subDays(7));
            })
            ->get();

        $cleanedCount = 0;

        foreach ($expiredTasks as $task) {
            // 刪除相關檔案
            if ($task->zip_file_path && file_exists(storage_path('app/' . $task->zip_file_path))) {
                unlink(storage_path('app/' . $task->zip_file_path));
            }

            // 更新狀態為過期或直接刪除
            $task->update(['status' => static::STATUS_EXPIRED]);
            $cleanedCount++;
        }

        return $cleanedCount;
    }

    /**
     * 根據任務ID查找任務
     *
     * @param string $taskId
     * @return static|null
     */
    public static function findByTaskId(string $taskId): ?static
    {
        return static::where('task_id', $taskId)->first();
    }

    /**
     * 檢查任務是否可以重試
     */
    public function canRetry(): bool
    {
        return in_array($this->status, [self::STATUS_FAILED, self::STATUS_EXPIRED]);
    }

    /**
     * 檢查任務是否可以取消
     */
    public function canCancel(): bool
    {
        return $this->status === self::STATUS_PROCESSING;
    }

    /**
     * 獲取進度百分比文字
     */
    public function getProgressTextAttribute(): string
    {
        return $this->progress . '%';
    }

    /**
     * 獲取任務狀態的中文描述（Attribute版本）
     */
    public function getStatusTextAttribute(): string
    {
        return $this->getStatusDescription();
    }
}
