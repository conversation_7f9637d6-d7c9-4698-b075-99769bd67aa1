<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Jobs\ProcessPdfMergeJob;
use App\Models\PdfMergeTask;
use App\Models\RecommendationLetter;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

/**
 * PDF壓縮API控制器
 * 
 * 提供外部系統調用的PDF壓縮功能API
 */
class PdfMergeApiController extends Controller
{
    /**
     * 啟動PDF壓縮任務
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function startMerge(Request $request): JsonResponse
    {
        try {
            // 驗證請求參數
            $validator = Validator::make($request->all(), [
                'exam_id' => 'required|string|max:50',
                'exam_year' => 'required|integer|min:100|max:200',
                'test_mode' => 'nullable|string|in:stress_test,large_files,slow_processing,transmission_test',
                'test_file_count' => 'nullable|integer|min:1|max:1000',
                'test_delay_seconds' => 'nullable|integer|min:1|max:300',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => '參數驗證失敗',
                    'errors' => $validator->errors()
                ], 422);
            }

            // 準備壓縮參數
            $parameters = array_filter([
                'exam_id' => $request->input('exam_id'),
                'exam_year' => $request->input('exam_year'),
                'test_mode' => $request->input('test_mode'),
                'test_file_count' => $request->input('test_file_count'),
                'test_delay_seconds' => $request->input('test_delay_seconds'),
                'requested_at' => now()->toISOString(),
                'client_ip' => $request->ip(),
            ]);

            // 測試模式處理
            if (!empty($parameters['test_mode'])) {
                Log::info('[TEST] 啟動測試模式', [
                    'test_mode' => $parameters['test_mode'],
                    'test_file_count' => $parameters['test_file_count'] ?? 'default',
                    'test_delay_seconds' => $parameters['test_delay_seconds'] ?? 'default',
                    'client_ip' => $request->ip()
                ]);

                $availableRecommendations = $this->generateTestRecommendations($parameters);
            } else {
                // 檢查是否有符合條件的推薦函
                $availableRecommendations = $this->checkAvailableRecommendations($parameters);
            }

            if ($availableRecommendations['total_count'] === 0) {
                return response()->json([
                    'status' => 'error',
                    'message' => '沒有找到符合條件的已提交推薦函',
                    'details' => [
                        'exam_id' => $parameters['exam_id'],
                        'exam_year' => $parameters['exam_year'],
                        'total_recommendations' => 0,
                        'applicants_with_recommendations' => 0,
                        'test_mode' => $parameters['test_mode'] ?? null
                    ]
                ], 404);
            }

            // 創建壓縮任務
            $task = PdfMergeTask::createTask($parameters);

            // 設定預期的檔案數量
            $task->update([
                'total_files' => $availableRecommendations['applicant_count']
            ]);

            // 檢測傳輸穩定性（測試模式）
            if (!empty($parameters['test_mode']) && $parameters['test_mode'] === 'transmission_test') {
                $transmissionCheck = $this->checkTransmissionStability($request);
                if (!$transmissionCheck['stable']) {
                    return response()->json([
                        'status' => 'transmission_error',
                        'message' => '檢測到傳輸不穩定，建議使用替代方案',
                        'details' => [
                            'issue' => $transmissionCheck['issue'],
                            'alternative_methods' => [
                                'direct_download' => '直接下載個別檔案',
                                'batch_processing' => '分批處理',
                                'retry_later' => '稍後重試'
                            ],
                            'retry_after' => 300 // 建議5分鐘後重試
                        ]
                    ], 503);
                }
            }

            // 派發背景任務
            Log::info('[TASK] PDF壓縮任務執行', [
                'task_id' => $task->task_id,
                'queue' => config('queue.default'),
                'test_mode' => $parameters['test_mode'] ?? null
            ]);

            try {
                // 派發 PDF 壓縮任務
                // sync 模式下會立即執行並直接修改任務狀態
                // 非 sync 模式則會將任務排入 queue 等待 background worker 處理
                ProcessPdfMergeJob::dispatch($task->task_id, $parameters);


                Log::info('[DISPATCH] PDF壓縮任務已派發', [
                    'task_id' => $task->task_id,
                    'queue_connection' => config('queue.default'),
                    'execution_mode' => config('queue.default') === 'sync' ? 'immediate (sync)' : 'queued',
                ]);
            } catch (\Exception $e) {
                Log::error('[ERROR] PDF壓縮任務執行失敗', [
                    'task_id' => $task->task_id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);

                // 標記任務失敗
                $task->markAsFailed('Job派發失敗: ' . $e->getMessage());

                return response()->json([
                    'status' => 'error',
                    'message' => 'PDF壓縮任務派發失敗',
                    'details' => [
                        'error' => $e->getMessage()
                    ]
                ], 500);
            }

            Log::info('[START] PDF壓縮任務已啟動', ['task_id' => $task->task_id]);

            return response()->json([
                'status' => 'processing',
                'task_id' => $task->task_id,
                'message' => '壓縮任務已啟動，請使用task_id查詢進度'
            ]);
        } catch (\Exception $e) {
            Log::error('啟動PDF壓縮任務失敗', [
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
                'client_ip' => $request->ip()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => '啟動壓縮任務失敗，請稍後再試'
            ], 500);
        }
    }

    /**
     * 查詢壓縮任務狀態
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getMergeStatus(Request $request): JsonResponse
    {
        Log::info('[REQUEST] 收到任務狀態查詢請求', [
            'query' => $request->query(),
            'input' => $request->all(),
            'ip' => $request->ip(),
        ]);

        if (!$request->has('task_id')) {
            Log::warning('[WARNING] 任務狀態查詢請求未提供 task_id', [
                'query' => $request->query(),
                'ip' => $request->ip(),
            ]);
        }

        try {

            $validator = Validator::make($request->all(), [
                'task_id' => 'required|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => '參數驗證失敗',
                    'errors' => $validator->errors()
                ], 422);
            }

            $taskId = $request->input('task_id');
            $task = PdfMergeTask::findByTaskId($taskId);

            if (!$task) {
                Log::warning('[NOT_FOUND] 查無任務', [
                    'task_id' => $taskId,
                    'ip' => $request->ip(),
                ]);

                return response()->json([
                    'status' => 'error',
                    'message' => '找不到指定的任務'
                ], 404);
            }


            // 檢查任務是否過期
            if ($task->isExpired()) {
                $task->update(['status' => PdfMergeTask::STATUS_EXPIRED]);

                return response()->json([
                    'status' => 'expired',
                    'message' => '任務已過期',
                    'task_id' => $taskId
                ]);
            }

            // 根據任務狀態返回不同的響應
            $response = [
                'status' => $task->status,
                'task_id' => $taskId,
                'progress' => $task->progress,
                'created_at' => $task->created_at->toISOString(),
            ];

            switch ($task->status) {
                case PdfMergeTask::STATUS_PROCESSING:
                    $response['message'] = '任務處理中';
                    $response['processed_files'] = $task->processed_files;
                    $response['total_files'] = $task->total_files;
                    break;

                case PdfMergeTask::STATUS_READY:
                    if ($task->isReady()) {
                        $response['message'] = '任務完成，可以下載';
                        $response['download_url'] = $task->download_url;
                        $response['expires_at'] = $task->expires_at?->toISOString();
                    } else {
                        $response['status'] = 'error';
                        $response['message'] = '檔案不存在或已損壞';
                    }
                    break;

                case PdfMergeTask::STATUS_FAILED:
                    $response['message'] = '任務處理失敗';
                    $response['error'] = $task->error_message;
                    break;

                case PdfMergeTask::STATUS_EXPIRED:
                    $response['message'] = '任務已過期';
                    break;

                default:
                    $response['message'] = '未知狀態';
                    break;
            }

            Log::info('[RESPONSE] 回傳任務狀態', [
                'task_id' => $taskId,
                'status' => $response['status'],
                'message' => $response['message'],
                'ip' => $request->ip(),
            ]);

            return response()->json($response);
        } catch (\Exception $e) {
            Log::error('查詢PDF壓縮任務狀態失敗', [
                'error' => $e->getMessage(),
                'task_id' => $request->input('task_id'),
                'client_ip' => $request->ip()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => '查詢任務狀態失敗，請稍後再試'
            ], 500);
        }
    }

    /**
     * 公共下載方法（無需認證）
     *
     * @param string $taskId
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function publicDownload(string $taskId, Request $request)
    {
        try {
            $task = PdfMergeTask::findByTaskId($taskId);

            if (!$task) {
                return response()->json([
                    'status' => 'error',
                    'message' => '找不到指定的任務'
                ], 404);
            }

            if (!$task->isReady()) {
                return response()->json([
                    'status' => 'error',
                    'message' => '檔案尚未準備好或已過期'
                ], 400);
            }

            // 使用 public 磁碟來獲取文件
            $disk = Storage::disk('public');

            if (!$disk->exists($task->zip_file_path)) {
                return response()->json([
                    'status' => 'error',
                    'message' => '檔案不存在'
                ], 404);
            }

            $filePath = $disk->path($task->zip_file_path);

            Log::info('公共PDF壓縮檔案下載', [
                'task_id' => $taskId,
                'file_path' => $task->zip_file_path,
                'client_ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            sleep(30); // 模擬處理延遲，實際應用中可移除

            return response()->download($filePath, basename($task->zip_file_path), [
                'Content-Type' => 'application/zip',
                'Content-Disposition' => 'attachment; filename="' . basename($task->zip_file_path) . '"',
                'Cache-Control' => 'no-cache, no-store, must-revalidate',
                'Pragma' => 'no-cache',
                'Expires' => '0'
            ]);
        } catch (\Exception $e) {
            Log::error('公共下載PDF壓縮檔案失敗', [
                'error' => $e->getMessage(),
                'task_id' => $taskId,
                'client_ip' => $request->ip()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => '下載失敗'
            ], 500);
        }
    }

    /**
     * 清理過期任務（內部API）
     * 
     * @return JsonResponse
     */
    public function cleanupExpiredTasks(): JsonResponse
    {
        try {
            $cleanedCount = PdfMergeTask::cleanupExpiredTasks();

            Log::info('PDF壓縮任務清理完成', [
                'cleaned_count' => $cleanedCount
            ]);

            return response()->json([
                'status' => 'success',
                'message' => "已清理 {$cleanedCount} 個過期任務",
                'cleaned_count' => $cleanedCount
            ]);
        } catch (\Exception $e) {
            Log::error('清理過期任務失敗', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => '清理過期任務失敗'
            ], 500);
        }
    }

    /**
     * 檢查可用的推薦函數量
     *
     * @param array $parameters
     * @return array
     */
    protected function checkAvailableRecommendations(array $parameters): array
    {
        $query = RecommendationLetter::where('status', RecommendationLetter::STATUS_SUBMITTED)
            ->whereNotNull('pdf_path');

        // 根據參數過濾
        if (isset($parameters['exam_id'])) {
            $query->where('exam_id', $parameters['exam_id']);
        }

        if (isset($parameters['exam_year'])) {
            $query->where('exam_year', $parameters['exam_year']);
        }

        // 獲取推薦函並按考生分組
        $recommendations = $query->with(['applicant'])->get();

        $groupedByApplicant = $recommendations->groupBy('applicant_id');

        // 檢查每個考生的PDF檔案是否存在
        $validApplicants = 0;
        $validRecommendations = 0;

        foreach ($groupedByApplicant as $applicantId => $applicantRecommendations) {
            $hasValidPdf = false;

            foreach ($applicantRecommendations as $recommendation) {
                if (Storage::disk('local')->exists($recommendation->pdf_path)) {
                    $validRecommendations++;
                    $hasValidPdf = true;
                }
            }

            if ($hasValidPdf) {
                $validApplicants++;
            }
        }

        return [
            'total_count' => $validRecommendations,
            'applicant_count' => $validApplicants,
            'total_applicants' => count($groupedByApplicant),
            'recommendations_by_applicant' => $groupedByApplicant->map(function ($recs) {
                return count($recs);
            })->toArray()
        ];
    }

    /**
     * 生成測試模式的推薦函數據
     *
     * @param array $parameters
     * @return array
     */
    private function generateTestRecommendations(array $parameters): array
    {
        $testMode = $parameters['test_mode'];
        $fileCount = $parameters['test_file_count'] ?? 10;

        switch ($testMode) {
            case 'stress_test':
                $fileCount = $parameters['test_file_count'] ?? 100;
                break;
            case 'large_files':
                $fileCount = $parameters['test_file_count'] ?? 50;
                break;
            case 'slow_processing':
                $fileCount = $parameters['test_file_count'] ?? 20;
                break;
            case 'transmission_test':
                $fileCount = $parameters['test_file_count'] ?? 30;
                break;
        }

        Log::info('[TEST] 生成測試數據', [
            'test_mode' => $testMode,
            'file_count' => $fileCount
        ]);

        return [
            'total_count' => $fileCount,
            'applicant_count' => max(1, intval($fileCount / 3)), // 假設平均每個考生3份推薦函
            'test_mode' => $testMode
        ];
    }

    /**
     * 檢測傳輸穩定性（測試用）
     *
     * @param Request $request
     * @return array
     */
    private function checkTransmissionStability(Request $request): array
    {
        // 模擬傳輸穩定性檢測
        $clientIp = $request->ip();
        $userAgent = $request->userAgent();

        // 模擬不同的傳輸問題
        $issues = [
            'network_latency' => '網路延遲過高',
            'bandwidth_limited' => '頻寬受限',
            'connection_unstable' => '連線不穩定',
            'server_overload' => '伺服器負載過高'
        ];

        // 隨機模擬傳輸問題（30%機率）
        if (rand(1, 100) <= 30) {
            $issueKey = array_rand($issues);

            Log::warning('[TRANSMISSION] 檢測到傳輸問題', [
                'client_ip' => $clientIp,
                'user_agent' => $userAgent,
                'issue' => $issueKey,
                'description' => $issues[$issueKey]
            ]);

            return [
                'stable' => false,
                'issue' => $issues[$issueKey],
                'issue_code' => $issueKey
            ];
        }

        Log::info('[TRANSMISSION] 傳輸穩定性檢測通過', [
            'client_ip' => $clientIp
        ]);

        return [
            'stable' => true,
            'issue' => null,
            'issue_code' => null
        ];
    }
}
