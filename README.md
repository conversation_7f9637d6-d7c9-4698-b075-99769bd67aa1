## 🧪 資料庫初始化與測試資料

### 執行 Migration 清除資料表 並填入 Seed 資料

```bash
php artisan migrate:fresh --seed

php artisan queue:work
php artisan queue:work --queue=pdf

php artisan queue:work --once	# 只處理一個任務後結束	測試時用，非長期執行
php artisan queue:restart	# 重啟所有正在執行的 queue worker	會觸發 worker 重載新程式碼
php artisan queue:failed	# 列出失敗的任務	顯示失敗任務清單
php artisan queue:retry {id}	# 重新嘗試執行失敗任務（依照失敗任務的 ID）
php artisan queue:flush	# 清除所有失敗的任務記錄
php artisan queue:forget {id}	# 刪除指定失敗任務記錄
php artisan queue:listen	# 類似 queue:work，但每執行完一個任務會重新載入框架	開發時用，效能比 queue:work 差

```

---

FOOTER (以到底部提示)

功能：

```
盡量與PB脫鉤
- 讓作業人員能夠在不依賴PB的狀態下完成作業
-
```

TODO

- 排程配置(自動清理、自動同步)
- 寫測試(API端、推薦函系統端)

- PDF任務壓力測試(延遲、大容量)
