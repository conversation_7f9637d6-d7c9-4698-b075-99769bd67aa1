# ProcessPdfMergeJob統一物件操作修復

## 問題描述

ProcessPdfMergeJob在測試模式下出現 "Cannot use object of type stdClass as array" 錯誤，原因是：

1. **正常模式**: 使用Eloquent模型，返回對象格式
2. **測試模式**: 使用stdClass對象，但代碼中混用了數組訪問語法
3. **不一致性**: 同一個方法需要處理不同的數據格式

### 錯誤位置
```
Cannot use object of type stdClass as array ... ProcessPdfMergeJob.php:226
```

## 解決方案

### 1. 統一物件操作策略

將所有數據訪問統一為物件操作，並在方法內部進行格式轉換：

```php
// 統一轉換為物件操作
if (is_array($recommendation)) {
    $recommendation = (object) $recommendation;
}

// 使用物件語法訪問
$pdfPath = $recommendation->pdf_path ?? null;
$recId = $recommendation->id ?? null;
```

### 2. 修改的方法

#### `groupRecommendationsByExternalAutono()`

**修改前:**
```php
return $recommendations->groupBy('external_autono')->toArray();
```

**修改後:**
```php
$grouped = [];

foreach ($recommendations as $recommendation) {
    // 統一轉換為物件操作
    if (is_array($recommendation)) {
        $recommendation = (object) $recommendation;
    }
    
    $externalAutono = $recommendation->external_autono ?? 'unknown';
    
    if (!isset($grouped[$externalAutono])) {
        $grouped[$externalAutono] = [];
    }
    
    $grouped[$externalAutono][] = $recommendation;
}

return $grouped;
```

#### `prepareApplicantFiles()`

**修改前:**
```php
$applicant = $recommendations[0]['applicant'] ?? null;
$autono = $recommendations[0]['external_autono'] ?? $applicant['id'] ?? 'unknown';
```

**修改後:**
```php
// 統一轉換為物件操作
$firstRec = $recommendations[0];
if (is_array($firstRec)) {
    $firstRec = (object) $firstRec;
}

// 獲取考生資訊
$applicant = $firstRec->applicant ?? null;
if (is_array($applicant)) {
    $applicant = (object) $applicant;
}

$autono = $firstRec->external_autono ?? $applicant->id ?? 'unknown';
```

### 3. 測試模式優化

在測試模式下添加了檔案路徑檢測：

```php
if ($isTestMode) {
    // 測試模式：創建虛擬檔案路徑
    $actualPath = $this->createTestPdfPath($recId);
    Log::debug('[TEST_PDF_CREATE]', [
        'task_id' => $this->taskId,
        'recommendation_id' => $recId,
        'test_path' => $actualPath
    ]);
} else {
    // 正常模式：檢查檔案是否存在
    $disk = Storage::disk('local');
    // ...
}
```

## 前端頁面調整

### 1. 語意調整

將所有「PDF合併」相關文字調整為「PDF壓縮」：

- 頁面標題: `PDF合併任務管理` → `PDF壓縮任務管理`
- 麵包屑: `PDF合併任務管理` → `PDF壓縮任務管理`
- 卡片標題: `PDF合併任務` → `PDF壓縮任務`
- 模態框標題: `創建PDF合併任務` → `創建PDF壓縮任務`

### 2. 新增測試功能

#### 測試模式按鈕
```tsx
<Button 
    size="sm" 
    variant="outline"
    onClick={() => {
        setNewTask({ exam_id: 'TEST2024', exam_year: 113, test_mode: true });
        setShowCreateModal(true);
    }}
>
    <FileText className="mr-1 h-3 w-3" />
    <span className="text-xs">測試模式</span>
</Button>
```

#### 測試模式選項
```tsx
<div className="flex items-center space-x-2">
    <input
        id="test_mode"
        type="checkbox"
        checked={newTask.test_mode}
        onChange={(e) => setNewTask((prev) => ({ ...prev, test_mode: e.target.checked }))}
        className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
    />
    <Label htmlFor="test_mode" className="text-sm">
        測試模式（產生約3GB測試檔案）
    </Label>
</div>
```

#### 動態描述
```tsx
<CardDescription>
    {newTask.test_mode 
        ? '測試模式：將產生約3GB的測試檔案進行壓縮' 
        : '指定考試ID和年度來創建新的壓縮任務'
    }
</CardDescription>
```

## 測試驗證

### 兼容性測試

創建了完整的測試腳本驗證三種情況：

1. **正常模式**: Eloquent對象格式
2. **測試模式**: stdClass對象格式  
3. **混合模式**: 數組和對象混合格式

### 測試結果

```
=== 測試1: 正常模式 ===
✓ 正常模式分組成功: 1 組
✓ 正常模式檔案準備成功: 0 個檔案

=== 測試2: 測試模式 ===
✓ 測試數據生成成功: 1536 筆
✓ 測試模式分組成功: 512 組
✓ 測試模式檔案準備成功: 3 個檔案

=== 測試3: 混合數據類型 ===
✓ 混合數據分組成功: 2 組
✓ 混合數據檔案準備成功 (autono_array): 0 個檔案
✓ 混合數據檔案準備成功 (autono_object): 0 個檔案

🎉 所有物件操作兼容性測試通過！
```

## 技術特點

### 1. 統一數據處理
- 所有數據訪問統一使用物件語法
- 自動轉換數組為物件格式
- 保持向後兼容性

### 2. 類型安全
- 使用 `is_array()` 檢查數據類型
- 安全的物件屬性訪問
- 預設值處理

### 3. 測試友好
- 支援多種數據格式
- 測試模式獨立處理
- 完整的錯誤處理

## 相關檔案

### 修改的檔案
- `app/Jobs/ProcessPdfMergeJob.php`
  - `groupRecommendationsByExternalAutono()` 方法
  - `prepareApplicantFiles()` 方法
- `resources/js/pages/admin/pdf-merge-tasks.tsx`
  - 語意調整
  - 測試功能新增

### 主要變更點
1. **統一物件操作**: 所有數據訪問使用物件語法
2. **格式轉換**: 自動轉換數組為物件
3. **測試模式**: 獨立的檔案處理邏輯
4. **前端優化**: 測試功能和語意調整

## 效果

✅ **錯誤消除**: 不再出現 "Cannot use object of type stdClass as array" 錯誤
✅ **統一操作**: 所有數據處理使用一致的物件語法
✅ **向後兼容**: 支援原有的Eloquent模型格式
✅ **測試友好**: 完整的測試模式支援
✅ **前端優化**: 直觀的測試功能和正確的語意

ProcessPdfMergeJob現在完全支援統一的物件操作，解決了測試模式下的兼容性問題！
