# PDF合併Job對象/數組兼容性修復

## 問題描述

在實現測試模式功能後，PDF合併Job出現了 "Cannot use object of type stdClass as array" 錯誤。

### 錯誤日誌
```
[2025-07-29 15:00:07] local.ERROR: [FINAL_FAILED] PDF壓縮Job最終失敗 
{
    "task_id":"merge_B7hrz3pWfTYNjj0u_1753772407",
    "exception_class":"Error",
    "error":"Cannot use object of type stdClass as array",
    "error_file":"C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Jobs\\ProcessPdfMergeJob.php",
    "error_line":348
}
```

## 根本原因

測試模式下的 `generateTestRecommendations()` 方法創建的推薦函數據是 `stdClass` 對象格式：

```php
$testRecommendations->push((object)[
    'id' => 'test_rec_' . $i,
    'applicant_id' => $applicantId,
    'external_autono' => $externalAutono,
    // ...
]);
```

但在 `prepareApplicantFiles()` 方法中使用了數組語法來訪問數據：

```php
$applicant = $recommendations[0]['applicant'] ?? null;  // ❌ 對象無法用數組語法
```

## 修復方案

### 1. 修改 `prepareApplicantFiles()` 方法

添加對象/數組格式的兼容性檢查：

```php
// 修復前
$applicant = $recommendations[0]['applicant'] ?? null;

// 修復後
$firstRec = $recommendations[0];
$applicant = is_array($firstRec) ? ($firstRec['applicant'] ?? null) : ($firstRec->applicant ?? null);
```

### 2. 修改 `groupRecommendationsByExternalAutono()` 方法

原本使用 Laravel Collection 的 `groupBy()` 方法，但對 `stdClass` 對象支持有限：

```php
// 修復前
return $recommendations->groupBy('external_autono')->toArray();

// 修復後
$grouped = [];
foreach ($recommendations as $recommendation) {
    $externalAutono = is_array($recommendation) 
        ? ($recommendation['external_autono'] ?? 'unknown')
        : ($recommendation->external_autono ?? 'unknown');
    
    if (!isset($grouped[$externalAutono])) {
        $grouped[$externalAutono] = [];
    }
    
    // 統一轉換為數組格式
    if (is_object($recommendation)) {
        $recArray = [
            'id' => $recommendation->id ?? null,
            'applicant_id' => $recommendation->applicant_id ?? null,
            // ...
        ];
        $grouped[$externalAutono][] = $recArray;
    } else {
        $grouped[$externalAutono][] = $recommendation;
    }
}
```

### 3. 統一數據訪問模式

在所有需要訪問推薦函數據的地方，都使用兼容性檢查：

```php
$pdfPath = is_array($recommendation) 
    ? ($recommendation['pdf_path'] ?? null) 
    : ($recommendation->pdf_path ?? null);
```

## 測試驗證

### 單元測試

創建了 `ProcessPdfMergeJobTest` 來測試：
- 對象格式數據的處理
- 數組格式數據的向後兼容性

### 集成測試

創建了簡單的測試腳本驗證整個流程：
- 生成測試推薦函數據
- 推薦函分組
- 檔案準備

## 修復結果

✅ **測試通過**: 所有單元測試和集成測試都通過
✅ **向後兼容**: 原有的數組格式數據仍然正常工作
✅ **測試模式**: 新的測試模式功能正常運行
✅ **錯誤消除**: 不再出現 "Cannot use object of type stdClass as array" 錯誤

## 影響範圍

### 修改的文件
- `app/Jobs/ProcessPdfMergeJob.php`
  - `prepareApplicantFiles()` 方法
  - `groupRecommendationsByExternalAutono()` 方法

### 新增的文件
- `tests/Unit/ProcessPdfMergeJobTest.php` - 單元測試
- `docs/fixes/pdf-merge-job-object-array-compatibility.md` - 修復文檔

## 預防措施

1. **類型檢查**: 在處理動態數據時，始終檢查數據類型
2. **統一格式**: 考慮在數據處理的早期階段統一數據格式
3. **測試覆蓋**: 為不同數據格式編寫測試用例
4. **文檔說明**: 明確記錄數據格式要求

## 學習要點

1. **Laravel Collection**: `groupBy()` 方法對對象屬性的支持有限
2. **PHP類型**: `stdClass` 對象和數組的訪問語法不同
3. **向後兼容**: 修復時要考慮現有代碼的兼容性
4. **測試驅動**: 先寫測試，再進行修復，確保修復有效
